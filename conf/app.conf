# 应用配置
appname = bed_sharing_system
httpport = 8080
runmode = dev

# 数据库配置
mysqluser = "root"
mysqlpass = "mysql_EmrBwc"
mysqlurls = "127.0.0.1"
mysqlport = "3306"
mysqldb = "bed_sharing_system"

# 模板配置
autorender = true
copyrequestbody = true
EnableDocs = true

# 静态文件配置
StaticDir = static:static

# 会话配置
SessionOn = true
SessionProvider = memory
SessionName = beegosessionID
SessionGCMaxLifetime = 3600

# 日志配置
EnableAdmin = true
AdminAddr = localhost
AdminPort = 8088