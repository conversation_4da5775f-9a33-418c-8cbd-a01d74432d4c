// 统计页面JavaScript
let occupancyChart, genderChart, revenueChart, roomTypeChart;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initCharts();
    loadDashboardData();
    loadChartData();
    
    // 设置定时刷新实时数据
    setInterval(loadRealTimeData, 30000); // 30秒刷新一次
    
    // 绑定事件
    document.getElementById('trend-period').addEventListener('change', loadOccupancyTrend);
    document.getElementById('revenue-period').addEventListener('change', loadRevenueStats);
});

// 初始化图表
function initCharts() {
    // 入住率趋势图
    const occupancyCtx = document.getElementById('occupancy-trend-chart').getContext('2d');
    occupancyChart = new Chart(occupancyCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '入住床位数',
                data: [],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }, {
                label: '入住人数',
                data: [],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 性别分布饼图
    const genderCtx = document.getElementById('gender-distribution-chart').getContext('2d');
    genderChart = new Chart(genderCtx, {
        type: 'doughnut',
        data: {
            labels: ['男性', '女性'],
            datasets: [{
                data: [0, 0],
                backgroundColor: ['#3b82f6', '#ec4899'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 收入统计图
    const revenueCtx = document.getElementById('revenue-chart').getContext('2d');
    revenueChart = new Chart(revenueCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '总收入',
                data: [],
                backgroundColor: '#10b981',
                borderRadius: 4
            }, {
                label: '已收款',
                data: [],
                backgroundColor: '#3b82f6',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 房间类型分布图
    const roomTypeCtx = document.getElementById('room-type-chart').getContext('2d');
    roomTypeChart = new Chart(roomTypeCtx, {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 加载仪表板数据
function loadDashboardData() {
    fetch('/stats/dashboard')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.data;
                
                // 更新统计卡片
                document.getElementById('total-rooms').textContent = stats.room_stats.total_rooms;
                document.getElementById('occupied-beds').textContent = stats.room_stats.occupied_beds;
                document.getElementById('active-orders').textContent = stats.order_stats.active_orders;
                document.getElementById('total-revenue').textContent = '¥' + (stats.order_stats.total_revenue || 0).toFixed(2);
                
                // 更新性别分布图
                if (stats.guest_stats.gender_distribution) {
                    const genderData = stats.guest_stats.gender_distribution;
                    genderChart.data.datasets[0].data = [genderData.male || 0, genderData.female || 0];
                    genderChart.update();
                }
            }
        })
        .catch(error => {
            console.error('加载仪表板数据失败:', error);
        });
}

// 加载图表数据
function loadChartData() {
    loadOccupancyTrend();
    loadRevenueStats();
    loadRoomTypeStats();
}

// 加载入住率趋势
function loadOccupancyTrend() {
    const days = document.getElementById('trend-period').value;
    
    fetch(`/stats/occupancy-trend?days=${days}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const trendData = data.data;
                const labels = trendData.map(item => item.date);
                const occupiedBeds = trendData.map(item => item.occupied_beds);
                const checkIns = trendData.map(item => item.check_ins);
                
                occupancyChart.data.labels = labels;
                occupancyChart.data.datasets[0].data = occupiedBeds;
                occupancyChart.data.datasets[1].data = checkIns;
                occupancyChart.update();
            }
        })
        .catch(error => {
            console.error('加载入住率趋势失败:', error);
        });
}

// 加载收入统计
function loadRevenueStats() {
    const period = document.getElementById('revenue-period').value;
    
    fetch(`/stats/revenue?period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const revenueData = data.data;
                const labels = revenueData.map(item => item.period);
                const totalRevenue = revenueData.map(item => parseFloat(item.total_revenue) || 0);
                const paidRevenue = revenueData.map(item => parseFloat(item.paid_revenue) || 0);
                
                revenueChart.data.labels = labels;
                revenueChart.data.datasets[0].data = totalRevenue;
                revenueChart.data.datasets[1].data = paidRevenue;
                revenueChart.update();
            }
        })
        .catch(error => {
            console.error('加载收入统计失败:', error);
        });
}

// 加载房间类型统计
function loadRoomTypeStats() {
    fetch('/stats/room-types')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const roomTypeData = data.data;
                const labels = roomTypeData.map(item => item.room_type);
                const counts = roomTypeData.map(item => parseInt(item.room_count) || 0);
                
                roomTypeChart.data.labels = labels;
                roomTypeChart.data.datasets[0].data = counts;
                roomTypeChart.update();
            }
        })
        .catch(error => {
            console.error('加载房间类型统计失败:', error);
        });
}

// 加载实时数据
function loadRealTimeData() {
    fetch('/stats/realtime')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const realTimeData = data.data;
                
                document.getElementById('current-occupancy').textContent = realTimeData.current_occupancy || 0;
                document.getElementById('today-checkins').textContent = realTimeData.today_checkins || 0;
                document.getElementById('today-checkouts').textContent = realTimeData.today_checkouts || 0;
                document.getElementById('available-beds').textContent = realTimeData.available_beds || 0;
                
                // 更新时间戳
                const now = new Date();
                document.getElementById('last-update').textContent = now.toLocaleTimeString();
            }
        })
        .catch(error => {
            console.error('加载实时数据失败:', error);
        });
}

// 刷新统计数据
function refreshStats() {
    loadDashboardData();
    loadChartData();
    loadRealTimeData();
}

// 导出统计报表
function exportStats() {
    const reportType = 'dashboard';
    const format = 'json';
    
    const url = `/stats/export?type=${reportType}&format=${format}`;
    window.open(url, '_blank');
}