// 房态管理页面JavaScript
class RoomManagement {
    constructor() {
        this.currentFilter = 'all';
        this.rooms = [];
        this.stats = {};
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.startAutoRefresh();
    }

    // 绑定事件
    bindEvents() {
        // 筛选标签点击事件
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.handleFilterChange(e.target.closest('.filter-tab'));
            });
        });

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshRoomData();
            });
        }

        // 一键分配按钮
        const quickAssignBtn = document.getElementById('quickAssignBtn');
        if (quickAssignBtn) {
            quickAssignBtn.addEventListener('click', () => {
                this.showQuickAssignModal();
            });
        }

        // 房间卡片点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.room-card')) {
                this.handleRoomCardClick(e);
            }
        });

        // 分配按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.assign-btn')) {
                e.stopPropagation();
                const roomId = e.target.closest('.assign-btn').getAttribute('data-room-id');
                this.showAssignModal(roomId);
            }
        });

        // 详情按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.detail-btn')) {
                e.stopPropagation();
                const roomId = e.target.closest('.detail-btn').getAttribute('data-room-id');
                this.showRoomDetail(roomId);
            }
        });

        // 快速分配表单提交
        const quickAssignForm = document.getElementById('quickAssignForm');
        if (quickAssignForm) {
            quickAssignForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleQuickAssign(e.target);
            });
        }

        // 性别冲突模态框事件
        const cancelAssignBtn = document.getElementById('cancelAssign');
        const forceAssignBtn = document.getElementById('forceAssign');
        
        if (cancelAssignBtn) {
            cancelAssignBtn.addEventListener('click', () => {
                BedSharingSystem.closeModal(document.getElementById('genderConflictModal'));
            });
        }

        if (forceAssignBtn) {
            forceAssignBtn.addEventListener('click', () => {
                this.forceAssignGuest();
            });
        }
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            await this.loadRoomData();
            await this.loadStatsData();
        } catch (error) {
            console.error('加载初始数据失败:', error);
            BedSharingSystem.showMessage('加载数据失败，请刷新页面重试', 'error');
        }
    }

    // 加载房间数据
    async loadRoomData() {
        try {
            const response = await BedSharingSystem.request('/rooms');
            if (response.success) {
                this.rooms = response.data;
                this.renderRooms();
            }
        } catch (error) {
            console.error('加载房间数据失败:', error);
            throw error;
        }
    }

    // 加载统计数据
    async loadStatsData() {
        try {
            const response = await BedSharingSystem.request('/rooms/stats');
            if (response.success) {
                this.stats = response.data;
                this.updateStatsDisplay();
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            throw error;
        }
    }

    // 处理筛选变化
    handleFilterChange(tab) {
        // 更新活跃状态
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        tab.classList.add('active');

        // 获取筛选类型
        const filter = tab.getAttribute('data-filter');
        this.currentFilter = filter;

        // 应用筛选
        this.applyFilter(filter);
    }

    // 应用筛选
    applyFilter(filter) {
        const roomCards = document.querySelectorAll('.room-card');
        
        roomCards.forEach(card => {
            let shouldShow = true;
            
            switch (filter) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'available':
                    shouldShow = card.getAttribute('data-status') === 'available';
                    break;
                case 'male':
                    shouldShow = card.getAttribute('data-gender') === 'male';
                    break;
                case 'female':
                    shouldShow = card.getAttribute('data-gender') === 'female';
                    break;
                case 'full':
                    shouldShow = card.getAttribute('data-status') === 'full';
                    break;
            }
            
            if (shouldShow) {
                card.style.display = 'block';
                card.classList.add('fade-in');
            } else {
                card.style.display = 'none';
                card.classList.remove('fade-in');
            }
        });
    }

    // 渲染房间列表
    renderRooms() {
        const roomGrid = document.getElementById('roomGrid');
        if (!roomGrid || !this.rooms) return;

        roomGrid.innerHTML = this.rooms.map(room => this.createRoomCardHTML(room)).join('');
        
        // 应用当前筛选
        this.applyFilter(this.currentFilter);
    }

    // 创建房间卡片HTML
    createRoomCardHTML(room) {
        const availableBeds = room.total_beds - room.occupied_beds;
        const genderIcon = this.getGenderIcon(room.current_gender);
        const genderText = this.getGenderText(room.current_gender);
        const statusText = this.getStatusText(room.room_status);
        
        return `
            <div class="room-card" 
                 data-room-id="${room.id}" 
                 data-status="${room.room_status}" 
                 data-gender="${room.current_gender}"
                 data-available="${availableBeds > 0}">
                
                <div class="room-status-indicator ${room.room_status} ${room.current_gender}"></div>
                
                <div class="room-number">${room.room_number}</div>
                
                <div class="bed-info">
                    <div class="bed-count">
                        <span class="occupied">${room.occupied_beds}</span>
                        <span class="separator">/</span>
                        <span class="total">${room.total_beds}</span>
                    </div>
                    <div class="bed-label">床位</div>
                </div>
                
                <div class="gender-indicator">
                    <i class="t-icon t-icon-user ${genderIcon}"></i>
                    <span>${genderText}</span>
                </div>
                
                <div class="room-status-text">${statusText}</div>
                
                <div class="room-actions">
                    ${availableBeds > 0 ? `
                        <button class="btn btn-sm btn-primary assign-btn" data-room-id="${room.id}">
                            <i class="t-icon t-icon-add"></i>
                            分配
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline detail-btn" data-room-id="${room.id}">
                        <i class="t-icon t-icon-view-list"></i>
                        详情
                    </button>
                </div>
                
                ${room.floor_number ? `<div class="floor-indicator">${room.floor_number}F</div>` : ''}
            </div>
        `;
    }

    // 获取性别图标
    getGenderIcon(gender) {
        switch (gender) {
            case 'male': return 'gender-male';
            case 'female': return 'gender-female';
            default: return 'gender-none';
        }
    }

    // 获取性别文本
    getGenderText(gender) {
        switch (gender) {
            case 'male': return '男';
            case 'female': return '女';
            default: return '空';
        }
    }

    // 获取状态文本
    getStatusText(status) {
        switch (status) {
            case 'available': return '空闲';
            case 'occupied': return '入住';
            case 'full': return '已满';
            case 'maintenance': return '维护';
            default: return '未知';
        }
    }

    // 更新统计显示
    updateStatsDisplay() {
        if (!this.stats) return;

        // 更新筛选标签中的数量
        const filterTabs = document.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            const filter = tab.getAttribute('data-filter');
            const countEl = tab.querySelector('.tab-count');
            if (countEl) {
                switch (filter) {
                    case 'all':
                        countEl.textContent = this.stats.total_rooms || 0;
                        break;
                    case 'available':
                        countEl.textContent = this.stats.available_rooms || 0;
                        break;
                    case 'male':
                        countEl.textContent = this.stats.male_rooms || 0;
                        break;
                    case 'female':
                        countEl.textContent = this.stats.female_rooms || 0;
                        break;
                    case 'full':
                        countEl.textContent = this.stats.full_rooms || 0;
                        break;
                }
            }
        });

        // 更新快速面板中的统计
        this.updateQuickPanelStats();
    }

    // 更新快速面板统计
    updateQuickPanelStats() {
        const statItems = document.querySelectorAll('.stat-item');
        if (statItems.length >= 4) {
            const totalBeds = this.stats.total_beds || 0;
            const occupiedBeds = this.stats.occupied_beds || 0;
            const availableBeds = totalBeds - occupiedBeds;
            const occupancyRate = totalBeds > 0 ? ((occupiedBeds / totalBeds) * 100).toFixed(1) : 0;

            statItems[0].querySelector('.stat-number').textContent = totalBeds;
            statItems[1].querySelector('.stat-number').textContent = occupiedBeds;
            statItems[2].querySelector('.stat-number').textContent = availableBeds;
            statItems[3].querySelector('.stat-number').textContent = occupancyRate + '%';
        }
    }

    // 处理房间卡片点击
    handleRoomCardClick(e) {
        const roomCard = e.target.closest('.room-card');
        const roomId = roomCard.getAttribute('data-room-id');
        
        // 如果点击的是按钮，不处理卡片点击
        if (e.target.closest('.btn')) {
            return;
        }
        
        this.showRoomDetail(roomId);
    }

    // 显示房间详情
    showRoomDetail(roomId) {
        window.location.href = `/rooms/${roomId}`;
    }

    // 显示分配模态框
    showAssignModal(roomId) {
        window.location.href = `/rooms/${roomId}/assign`;
    }

    // 显示快速分配模态框
    showQuickAssignModal() {
        // 滚动到快速分配区域
        const quickAssignSection = document.querySelector('.quick-assign-section');
        if (quickAssignSection) {
            quickAssignSection.scrollIntoView({ behavior: 'smooth' });
            
            // 聚焦到姓名输入框
            setTimeout(() => {
                const nameInput = quickAssignSection.querySelector('input[name="guest_name"]');
                if (nameInput) {
                    nameInput.focus();
                }
            }, 500);
        }
    }

    // 处理快速分配
    async handleQuickAssign(form) {
        const formData = new FormData(form);
        const guestName = formData.get('guest_name');
        const guestGender = formData.get('guest_gender');
        const guestPhone = formData.get('guest_phone');

        if (!guestName || !guestGender) {
            BedSharingSystem.showMessage('请填写完整的住客信息', 'error');
            return;
        }

        try {
            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="t-icon t-icon-loading"></i> 分配中...';
            submitBtn.disabled = true;

            // 寻找合适的房间
            const suitableRoom = this.findSuitableRoom(guestGender);
            
            if (!suitableRoom) {
                BedSharingSystem.showMessage('暂无合适的房间可分配', 'warning');
                return;
            }

            // 执行分配
            const response = await this.assignGuestToRoom(suitableRoom.id, {
                guest_name: guestName,
                guest_gender: guestGender,
                guest_phone: guestPhone,
                bed_number: 'A' // 自动选择第一个可用床位
            });

            if (response.success) {
                BedSharingSystem.showMessage('住客分配成功！', 'success');
                form.reset();
                await this.refreshRoomData();
            } else {
                if (response.type === 'gender_conflict') {
                    this.showGenderConflictModal(response.message, suitableRoom.id, {
                        guest_name: guestName,
                        guest_gender: guestGender,
                        guest_phone: guestPhone
                    });
                } else {
                    BedSharingSystem.showMessage(response.message || '分配失败', 'error');
                }
            }

        } catch (error) {
            console.error('快速分配失败:', error);
            BedSharingSystem.showMessage('分配失败，请重试', 'error');
        } finally {
            // 恢复按钮状态
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    // 寻找合适的房间
    findSuitableRoom(guestGender) {
        // 优先选择同性别的房间
        let suitableRooms = this.rooms.filter(room => 
            room.room_status === 'available' && 
            (room.current_gender === 'none' || room.current_gender === guestGender) &&
            room.occupied_beds < room.total_beds
        );

        // 按可用床位数排序，优先选择床位较多的房间
        suitableRooms.sort((a, b) => (b.total_beds - b.occupied_beds) - (a.total_beds - a.occupied_beds));

        return suitableRooms[0] || null;
    }

    // 分配住客到房间
    async assignGuestToRoom(roomId, guestData) {
        const response = await BedSharingSystem.request(`/rooms/${roomId}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(guestData).toString()
        });

        return response;
    }

    // 显示性别冲突模态框
    showGenderConflictModal(message, roomId, guestData) {
        const modal = document.getElementById('genderConflictModal');
        const messageEl = document.getElementById('conflictMessage');
        
        if (modal && messageEl) {
            messageEl.textContent = message;
            modal._roomId = roomId;
            modal._guestData = guestData;
            BedSharingSystem.showModal('genderConflictModal');
        }
    }

    // 强制分配住客
    async forceAssignGuest() {
        const modal = document.getElementById('genderConflictModal');
        const roomId = modal._roomId;
        const guestData = modal._guestData;

        if (!roomId || !guestData) {
            BedSharingSystem.closeModal(modal);
            return;
        }

        try {
            // 添加强制分配标记
            guestData.force_assign = 'true';
            
            const response = await this.assignGuestToRoom(roomId, guestData);
            
            if (response.success) {
                BedSharingSystem.showMessage('住客分配成功！', 'success');
                if (response.warning) {
                    BedSharingSystem.showMessage(response.warning, 'warning', 5000);
                }
                await this.refreshRoomData();
            } else {
                BedSharingSystem.showMessage(response.message || '分配失败', 'error');
            }

        } catch (error) {
            console.error('强制分配失败:', error);
            BedSharingSystem.showMessage('分配失败，请重试', 'error');
        } finally {
            BedSharingSystem.closeModal(modal);
        }
    }

    // 刷新房间数据
    async refreshRoomData() {
        try {
            await this.loadRoomData();
            await this.loadStatsData();
            BedSharingSystem.showMessage('数据已刷新', 'success', 1500);
        } catch (error) {
            console.error('刷新数据失败:', error);
            BedSharingSystem.showMessage('刷新失败，请重试', 'error');
        }
    }

    // 开始自动刷新
    startAutoRefresh() {
        // 每30秒自动刷新一次
        this.refreshInterval = setInterval(() => {
            this.refreshRoomData();
        }, 30000);
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 销毁
    destroy() {
        this.stopAutoRefresh();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.roomManagement = new RoomManagement();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.roomManagement) {
        window.roomManagement.destroy();
    }
});

// 计算入住率函数
function calculateOccupancyRate() {
    const totalBeds = parseInt(document.querySelector('.stat-item:nth-child(1) .stat-number').textContent) || 0;
    const occupiedBeds = parseInt(document.querySelector('.stat-item:nth-child(2) .stat-number').textContent) || 0;
    const rate = totalBeds > 0 ? ((occupiedBeds / totalBeds) * 100).toFixed(1) : 0;
    const rateElement = document.getElementById('occupancy-rate');
    if (rateElement) {
        rateElement.textContent = rate + '%';
    }
}

// 页面加载完成后计算入住率
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(calculateOccupancyRate, 100);
});