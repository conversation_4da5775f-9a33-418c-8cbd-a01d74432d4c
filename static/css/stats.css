/* 统计页面样式 */
.stats-overview {
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    height: 100px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.room-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bed-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

.charts-section {
    margin-bottom: 24px;
}

.chart-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.chart-controls {
    display: flex;
    gap: 12px;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.chart-content canvas {
    width: 100% !important;
    height: 100% !important;
}

.realtime-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.update-time {
    font-size: 14px;
    color: #6b7280;
}

.realtime-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.realtime-value {
    font-size: 32px;
    font-weight: bold;
    color: #1e40af;
    line-height: 1;
    margin-bottom: 8px;
}

.realtime-label {
    font-size: 14px;
    color: #64748b;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.content-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
        height: auto;
        padding: 16px;
    }
    
    .chart-content {
        height: 250px;
    }
    
    .content-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
}