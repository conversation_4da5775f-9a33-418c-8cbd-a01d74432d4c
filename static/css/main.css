/* TDesign CSS 导入 */
@import url('https://cdn.jsdelivr.net/npm/tdesign-web-components@1.0.0/dist/tdesign.min.css');

/* 自定义CSS变量 */
:root {
    --primary-color: #0052D9;
    --primary-light: #E7F3FF;
    --secondary-color: #F3F3F3;
    --success-color: #00A870;
    --warning-color: #ED7B2F;
    --error-color: #FF4D4F;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-placeholder: #CCCCCC;
    --border-color: #E7E7E7;
    --bg-color: #FAFAFA;
    --white: #FFFFFF;
    --male-color: #4A90E2;
    --female-color: #F5A623;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* 顶部导航栏 */
.header {
    background: var(--primary-color);
    color: var(--white);
    padding: 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    height: 64px;
}

.header-left .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
}

.logo .t-icon {
    font-size: 24px;
}

.header-center .nav-menu {
    display: flex;
    gap: 32px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: var(--white);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.nav-item:hover,
.nav-item.active {
    background: rgba(255, 255, 255, 0.1);
    opacity: 1;
}

.header-right .header-info {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
}

/* 主要内容区域 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    min-height: calc(100vh - 64px);
}

/* 房态管理页面 */
.room-management {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 24px;
    height: 100%;
}

/* 筛选栏 */
.filter-bar {
    grid-column: 1 / -1;
    background: var(--white);
    border-radius: 8px;
    padding: 20px 24px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
}

.filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-tabs {
    display: flex;
    gap: 8px;
}

.filter-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 20px;
    background: var(--secondary-color);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.filter-tab:hover {
    background: var(--primary-light);
}

.filter-tab.active {
    background: var(--primary-color);
    color: var(--white);
}

.tab-text {
    font-size: 14px;
    font-weight: 500;
}

.tab-count {
    font-size: 18px;
    font-weight: 600;
    margin-top: 4px;
}

.filter-actions {
    display: flex;
    gap: 12px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #003BA8;
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #008A5D;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--secondary-color);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* 房间网格 */
.room-grid-container {
    background: var(--white);
    border-radius: 8px;
    padding: 24px;
    box-shadow: var(--shadow);
}

.room-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

/* 房间卡片 */
.room-card {
    position: relative;
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.room-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

/* 房间状态指示器 */
.room-status-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 12px 12px 0 0;
}

.room-status-indicator.available {
    background: var(--success-color);
}

.room-status-indicator.occupied.male {
    background: var(--male-color);
}

.room-status-indicator.occupied.female {
    background: var(--female-color);
}

.room-status-indicator.full {
    background: var(--error-color);
}

.room-status-indicator.maintenance {
    background: var(--text-placeholder);
}

/* 房间号 */
.room-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 12px;
    text-align: center;
}

/* 床位信息 */
.bed-info {
    text-align: center;
    margin-bottom: 16px;
}

.bed-count {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

.bed-count .occupied {
    color: var(--primary-color);
}

.bed-count .separator {
    color: var(--text-placeholder);
    margin: 0 4px;
}

.bed-count .total {
    color: var(--text-secondary);
}

.bed-label {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 性别指示器 */
.gender-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
}

.gender-male {
    color: var(--male-color);
}

.gender-female {
    color: var(--female-color);
}

.gender-none {
    color: var(--text-placeholder);
}

/* 房间状态文本 */
.room-status-text {
    text-align: center;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 16px;
    padding: 4px 8px;
    background: var(--secondary-color);
    border-radius: 4px;
}

/* 房间操作按钮 */
.room-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

/* 楼层指示器 */
.floor-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--primary-light);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

/* 快速操作面板 */
.quick-panel {
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    height: fit-content;
    position: sticky;
    top: 88px;
}

.panel-header {
    padding: 20px 24px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.panel-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.panel-content {
    padding: 0 24px 24px;
}

/* 统计区域 */
.stats-section {
    margin-bottom: 32px;
}

.stats-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-item {
    text-align: center;
    padding: 16px 12px;
    background: var(--secondary-color);
    border-radius: 8px;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 快速分配区域 */
.quick-assign-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.form-control::placeholder {
    color: var(--text-placeholder);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: 12px;
    max-width: 480px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-hover);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 24px;
    text-align: center;
}

.warning-icon {
    font-size: 48px;
    color: var(--warning-color);
    margin-bottom: 16px;
}

.warning-message {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 24px;
    line-height: 1.6;
}

.warning-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .room-management {
        grid-template-columns: 1fr;
    }
    
    .quick-panel {
        position: static;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 16px;
    }
    
    .header-center .nav-menu {
        gap: 16px;
    }
    
    .nav-item {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .filter-container {
        flex-direction: column;
        gap: 16px;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
    }
    
    .room-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}