package routers

import (
	"bed_sharing_system/controllers"

	"github.com/astaxie/beego"
)

func init() {
	// 房态管理路由
	beego.Router("/", &controllers.RoomController{}, "get:Index")
	beego.Router("/rooms", &controllers.RoomController{}, "get:List")
	beego.Router("/rooms/stats", &controllers.RoomController{}, "get:Stats")
	beego.Router("/rooms/:id:int", &controllers.RoomController{}, "get:Detail")
	beego.Router("/rooms/:id:int/assign", &controllers.RoomController{}, "get,post:Assign")

	// 住客管理路由
	beego.Router("/guests", &controllers.GuestController{}, "get:List")
	beego.Router("/guests/new", &controllers.GuestController{}, "get,post:New")
	beego.Router("/guests/:id:int", &controllers.GuestController{}, "get:Detail")
	beego.Router("/guests/search", &controllers.GuestController{}, "post:Search")

	// 订单管理路由
	beego.Router("/orders", &controllers.OrderController{}, "get:List")
	beego.Router("/orders/new", &controllers.OrderController{}, "get,post:New")
	beego.Router("/orders/:id:int", &controllers.OrderController{}, "get:Detail")
	beego.Router("/orders/:id:int/checkin", &controllers.OrderController{}, "post:CheckIn")
	beego.Router("/orders/:id:int/checkout", &controllers.OrderController{}, "post:CheckOut")
	beego.Router("/orders/:id:int/status", &controllers.OrderController{}, "post:UpdateStatus")
	beego.Router("/orders/merge", &controllers.OrderController{}, "post:Merge")
	beego.Router("/orders/:id:int/split", &controllers.OrderController{}, "post:Split")

	// 分配相关路由
	beego.Router("/assignment/smart-assign", &controllers.AssignmentController{}, "post:SmartAssign")
	beego.Router("/assignment/test", &controllers.AssignmentController{}, "get:Test")

	// 统计报表路由
	beego.Router("/stats", &controllers.StatsController{}, "get:Index")
	beego.Router("/stats/dashboard", &controllers.StatsController{}, "get:Dashboard")
	beego.Router("/stats/occupancy-trend", &controllers.StatsController{}, "get:OccupancyTrend")
	beego.Router("/stats/revenue", &controllers.StatsController{}, "get:Revenue")
	beego.Router("/stats/room-types", &controllers.StatsController{}, "get:RoomTypes")
	beego.Router("/stats/gender-distribution", &controllers.StatsController{}, "get:GenderDistribution")
	beego.Router("/stats/realtime", &controllers.StatsController{}, "get:RealTime")
	beego.Router("/stats/export", &controllers.StatsController{}, "get:Export")

	// API路由
	beego.Router("/api/rooms/validate", &controllers.ApiController{}, "post:ValidateGender")
	beego.Router("/api/rooms/:id:int/available-beds", &controllers.ApiController{}, "get:GetAvailableBeds")
	beego.Router("/api/stats/dashboard", &controllers.ApiController{}, "get:GetDashboardStats")
}
