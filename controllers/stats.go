package controllers

import (
	"bed_sharing_system/models"
	"encoding/json"
	"strconv"
	"time"

	"github.com/astaxie/beego"
)

// 统计报表控制器
type StatsController struct {
	beego.Controller
}

// 统计报表主页
func (c *StatsController) Index() {
	c.Data["Title"] = "统计报表"
	c.Data["PageName"] = "统计报表"
	c.TplName = "stats/index.html"
}

// 获取仪表板统计数据
func (c *StatsController) Dashboard() {
	roomManager := &models.RoomManager{}
	orderManager := &models.OrderManager{}
	guestManager := &models.GuestManager{}

	// 获取房间统计
	roomStats, err := roomManager.GetRoomStats()
	if err != nil {
		beego.Error("获取房间统计失败:", err)
		roomStats = &models.RoomStats{}
	}

	// 获取订单统计
	orderStats, err := orderManager.GetOrderStats()
	if err != nil {
		beego.Error("获取订单统计失败:", err)
		orderStats = &models.OrderStats{}
	}

	// 获取住客统计
	guestStats, err := guestManager.GetGuestStats()
	if err != nil {
		beego.Error("获取住客统计失败:", err)
		guestStats = &models.GuestStats{}
	}

	// 计算入住率
	occupancyRate := float64(0)
	if roomStats.TotalBeds > 0 {
		occupancyRate = float64(roomStats.OccupiedBeds) / float64(roomStats.TotalBeds) * 100
	}

	dashboardData := map[string]interface{}{
		"room_stats": map[string]interface{}{
			"total_rooms":     roomStats.TotalRooms,
			"occupied_rooms":  roomStats.OccupiedRooms,
			"available_rooms": roomStats.AvailableRooms,
			"total_beds":      roomStats.TotalBeds,
			"occupied_beds":   roomStats.OccupiedBeds,
			"available_beds":  roomStats.AvailableBeds,
			"occupancy_rate":  occupancyRate,
		},
		"order_stats": orderStats,
		"guest_stats": guestStats,
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    dashboardData,
	}
	c.ServeJSON()
}

// 获取房间类型统计
func (c *StatsController) RoomTypes() {
	roomManager := &models.RoomManager{}
	roomTypeStats, err := roomManager.GetRoomTypeStats()
	if err != nil {
		beego.Error("获取房间类型统计失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取房间类型统计失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    roomTypeStats,
		}
	}
	c.ServeJSON()
}

// 获取性别分布统计
func (c *StatsController) GenderDistribution() {
	accommodationManager := &models.AccommodationManager{}
	genderStats, err := accommodationManager.GetGenderDistribution()
	if err != nil {
		beego.Error("获取性别分布统计失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取性别分布统计失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    genderStats,
		}
	}
	c.ServeJSON()
}

// 实时统计数据
func (c *StatsController) RealTime() {
	accommodationManager := &models.AccommodationManager{}
	roomManager := &models.RoomManager{}

	// 当前入住人数
	activeCount, _ := accommodationManager.GetActiveAccommodationCount()

	// 今日入住/退房数量
	today := time.Now().Format("2006-01-02")
	todayCheckIns, _ := accommodationManager.GetCheckInCountByDate(today)
	todayCheckOuts, _ := accommodationManager.GetCheckOutCountByDate(today)

	// 房间状态分布
	roomStats, _ := roomManager.GetRoomStats()

	realTimeData := map[string]interface{}{
		"current_occupancy": activeCount,
		"today_checkins":    todayCheckIns,
		"today_checkouts":   todayCheckOuts,
		"available_beds":    roomStats.AvailableBeds,
		"timestamp":         time.Now().Unix(),
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    realTimeData,
	}
	c.ServeJSON()
}

// 导出统计报表
func (c *StatsController) Export() {
	reportType := c.GetString("type", "dashboard")
	format := c.GetString("format", "json")

	var data interface{}
	var filename string

	switch reportType {
	case "occupancy":
		days, _ := strconv.Atoi(c.GetString("days", "30"))
		accommodationManager := &models.AccommodationManager{}
		trendData, err := accommodationManager.GetOccupancyTrend(days)
		if err != nil {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "导出失败",
			}
			c.ServeJSON()
			return
		}
		data = trendData
		filename = "occupancy_trend_" + time.Now().Format("20060102")
	case "revenue":
		period := c.GetString("period", "month")
		orderManager := &models.OrderManager{}
		revenueData, err := orderManager.GetRevenueStats(period)
		if err != nil {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "导出失败",
			}
			c.ServeJSON()
			return
		}
		data = revenueData
		filename = "revenue_stats_" + time.Now().Format("20060102")
	default:
		// 默认导出仪表板数据
		c.Dashboard()
		return
	}

	if format == "json" {
		c.Ctx.Output.Header("Content-Type", "application/json")
		c.Ctx.Output.Header("Content-Disposition", "attachment; filename="+filename+".json")

		jsonData, _ := json.MarshalIndent(data, "", "  ")
		c.Ctx.Output.Body(jsonData)
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "暂不支持该格式导出",
		}
		c.ServeJSON()
	}
}

// 获取入住率趋势数据
func (c *StatsController) OccupancyTrend() {
	days, _ := strconv.Atoi(c.GetString("days", "7"))

	accommodationManager := &models.AccommodationManager{}
	trendData, err := accommodationManager.GetOccupancyTrend(days)
	if err != nil {
		beego.Error("获取入住率趋势失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取入住率趋势失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    trendData,
		}
	}
	c.ServeJSON()
}

// 获取收入统计数据
func (c *StatsController) Revenue() {
	period := c.GetString("period", "month")

	orderManager := &models.OrderManager{}
	revenueData, err := orderManager.GetRevenueStats(period)
	if err != nil {
		beego.Error("获取收入统计失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取收入统计失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    revenueData,
		}
	}
	c.ServeJSON()
}
