package controllers

import (
	"bed_sharing_system/models"
	"strconv"
	"time"

	"github.com/astaxie/beego"
)

// 处理房间分配逻辑的辅助方法
func (c *AssignmentController) processRoomAssignment(selectedRoom *models.Room, guestName, guestGender, guestPhone, guestIdCard string, stayNights int) {
	// 检查房间性别兼容性
	if selectedRoom.CurrentGender != "" && selectedRoom.CurrentGender != guestGender {
		c.Data["json"] = map[string]interface{}{
			"success":       false,
			"message":       "性别冲突",
			"warning_type":  "gender_conflict",
			"warning_msg":   "该房间已有其他性别住客，无法分配",
			"conflict_room": selectedRoom,
		}
		c.<PERSON>ve<PERSON>()
		return
	}

	// 创建住客记录
	guestManager := &models.GuestManager{}
	guest := &models.Guest{
		Name:   guestName,
		Gender: guestGender,
		Phone:  guestPhone,
		IdCard: guestIdCard,
	}

	err := guestManager.CreateGuest(guest)
	if err != nil {
		beego.Error("创建住客失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "创建住客记录失败",
		}
		c.ServeJSON()
		return
	}

	// 创建订单
	orderManager := &models.OrderManager{}
	order := &models.Order{
		OrderNumber:  generateOrderNumber(),
		GuestId:      guest.Id,
		RoomId:       selectedRoom.Id,
		CheckInDate:  time.Now(),
		CheckOutDate: time.Now().AddDate(0, 0, stayNights),
		Nights:       stayNights,
		BedCount:     1,
		OrderStatus:  "confirmed",
	}

	err = orderManager.CreateOrder(order)
	if err != nil {
		beego.Error("创建订单失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "创建订单失败",
		}
		c.ServeJSON()
		return
	}

	// 分配床位
	accommodationManager := &models.AccommodationManager{}
	availableBeds, err := accommodationManager.GetAvailableBeds(selectedRoom.Id)
	if err != nil || len(availableBeds) == 0 {
		beego.Error("获取可用床位失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "该房间暂无可用床位",
		}
		c.ServeJSON()
		return
	}

	// 创建住宿记录
	accommodation := &models.Accommodation{
		OrderId:             order.Id,
		RoomId:              selectedRoom.Id,
		GuestId:             guest.Id,
		BedNumber:           availableBeds[0],
		AccommodationStatus: "active",
	}

	err = accommodationManager.CreateAccommodation(accommodation)
	if err != nil {
		beego.Error("创建住宿记录失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "分配床位失败",
		}
		c.ServeJSON()
		return
	}

	// 更新房间状态
	roomManager := &models.RoomManager{}
	selectedRoom.OccupiedBeds++
	if selectedRoom.CurrentGender == "" {
		selectedRoom.CurrentGender = guestGender
	}
	if selectedRoom.OccupiedBeds >= selectedRoom.TotalBeds {
		selectedRoom.RoomStatus = "full"
	} else {
		selectedRoom.RoomStatus = "partial"
	}

	// 使用现有的UpdateRoomStatus方法
	err = roomManager.UpdateRoomStatus(selectedRoom.Id, selectedRoom.OccupiedBeds, selectedRoom.CurrentGender)
	if err != nil {
		beego.Error("更新房间状态失败:", err)
	}

	// 返回成功结果
	c.Data["json"] = map[string]interface{}{
		"success":     true,
		"message":     "分配成功",
		"guest_id":    guest.Id,
		"order_id":    order.Id,
		"room_number": selectedRoom.RoomNumber,
		"bed_number":  availableBeds[0],
		"assignment_details": map[string]interface{}{
			"guest_name":  guestName,
			"room_number": selectedRoom.RoomNumber,
			"bed_number":  availableBeds[0],
			"check_in":    time.Now().Format("2006-01-02 15:04:05"),
			"nights":      stayNights,
		},
	}
	c.ServeJSON()
}

// 生成订单号
func generateOrderNumber() string {
	return "ORD" + time.Now().Format("20060102150405") + strconv.Itoa(int(time.Now().UnixNano()%1000))
}
