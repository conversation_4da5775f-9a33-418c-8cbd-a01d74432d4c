package controllers

import (
	"bed_sharing_system/models"
	"strconv"

	"github.com/astaxie/beego"
)

// 住客控制器
type GuestController struct {
	beego.Controller
}

// 住客列表
func (c *GuestController) List() {
	guestManager := &models.GuestManager{}

	// 获取分页参数
	page, _ := strconv.Atoi(c.GetString("page", "1"))
	pageSize, _ := strconv.Atoi(c.GetString("page_size", "20"))

	// 获取搜索参数
	keyword := c.GetString("keyword")

	var guests []*models.Guest
	var total int64
	var err error

	if keyword != "" {
		guests, err = guestManager.SearchGuests(keyword)
		total = int64(len(guests))
	} else {
		guests, total, err = guestManager.GetGuestsPaginated(page, pageSize)
	}

	if err != nil {
		beego.Error("获取住客列表失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取住客列表失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data": map[string]interface{}{
				"guests":    guests,
				"total":     total,
				"page":      page,
				"page_size": pageSize,
			},
		}
	}

	c.ServeJSON()
}

// 新建住客页面
func (c *GuestController) New() {
	if c.Ctx.Input.Method() == "GET" {
		c.Data["Title"] = "新建住客"
		c.Data["PageName"] = "住客管理"
		c.TplName = "guest/new.html"
	} else if c.Ctx.Input.Method() == "POST" {
		// 处理新建住客请求
		guestManager := &models.GuestManager{}

		guest := &models.Guest{
			Name:   c.GetString("name"),
			Gender: c.GetString("gender"),
			Phone:  c.GetString("phone"),
			IdCard: c.GetString("id_card"),
			Email:  c.GetString("email"),
		}

		// 验证必填字段
		if guest.Name == "" || guest.Gender == "" {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "姓名和性别为必填项",
			}
			c.ServeJSON()
			return
		}

		// 检查是否已存在
		exists, _, _ := guestManager.GuestExists(guest.Phone, guest.IdCard)
		if exists {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "该住客已存在",
			}
			c.ServeJSON()
			return
		}

		err := guestManager.CreateGuest(guest)
		if err != nil {
			beego.Error("创建住客失败:", err)
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "创建住客失败",
			}
		} else {
			c.Data["json"] = map[string]interface{}{
				"success": true,
				"message": "住客创建成功",
				"data":    guest,
			}
		}

		c.ServeJSON()
	}
}

// 住客详情
func (c *GuestController) Detail() {
	guestId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	guestManager := &models.GuestManager{}
	accommodationManager := &models.AccommodationManager{}

	// 获取住客信息
	guest, err := guestManager.GetGuestById(guestId)
	if err != nil {
		beego.Error("获取住客信息失败:", err)
		c.Abort("404")
		return
	}

	// 获取住宿历史
	accommodations, err := accommodationManager.GetAccommodationsByGuest(guestId)
	if err != nil {
		beego.Error("获取住宿历史失败:", err)
		accommodations = []*models.Accommodation{}
	}

	c.Data["Guest"] = guest
	c.Data["Accommodations"] = accommodations
	c.Data["Title"] = "住客详情 - " + guest.Name
	c.Data["PageName"] = "住客管理"
	c.TplName = "guest/detail.html"
}

// 搜索住客
func (c *GuestController) Search() {
	keyword := c.GetString("keyword")
	if keyword == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请输入搜索关键词",
		}
		c.ServeJSON()
		return
	}

	guestManager := &models.GuestManager{}
	guests, err := guestManager.SearchGuests(keyword)

	if err != nil {
		beego.Error("搜索住客失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "搜索失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    guests,
		}
	}

	c.ServeJSON()
}
