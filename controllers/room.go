package controllers

import (
	"bed_sharing_system/models"
	"strconv"
	"time"

	"github.com/astaxie/beego"
)

// 房间控制器
type RoomController struct {
	beego.Controller
}

// 首页 - 房态管理主页
func (c *RoomController) Index() {
	roomManager := &models.RoomManager{}

	// 获取所有房间
	rooms, err := roomManager.GetAllRooms()
	if err != nil {
		beego.Error("获取房间列表失败:", err)
		c.Data["Rooms"] = []*models.Room{}
	} else {
		c.Data["Rooms"] = rooms
	}

	// 获取房间统计
	stats, err := roomManager.GetRoomStats()
	if err != nil {
		beego.Error("获取房间统计失败:", err)
		c.Data["Stats"] = &models.RoomStats{}
	} else {
		c.Data["Stats"] = stats
	}

	c.Data["Title"] = "拼床住宿管理系统"
	c.Data["PageName"] = "房态管理"
	c.TplName = "room/index.html"
}

// 房间列表（支持筛选）
func (c *RoomController) List() {
	roomManager := &models.RoomManager{}

	// 获取筛选参数
	status := c.GetString("status")
	gender := c.GetString("gender")

	var rooms []*models.Room
	var err error

	if status != "" {
		rooms, err = roomManager.GetRoomsByStatus(status)
	} else if gender != "" {
		rooms, err = roomManager.GetAvailableRoomsByGender(gender)
	} else {
		rooms, err = roomManager.GetAllRooms()
	}

	if err != nil {
		beego.Error("获取房间列表失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取房间列表失败",
			"data":    nil,
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "获取成功",
			"data":    rooms,
		}
	}

	c.ServeJSON()
}

// 房间统计
func (c *RoomController) Stats() {
	roomManager := &models.RoomManager{}

	stats, err := roomManager.GetRoomStats()
	if err != nil {
		beego.Error("获取房间统计失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取统计数据失败",
			"data":    nil,
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "获取成功",
			"data":    stats,
		}
	}

	c.ServeJSON()
}

// 房间详情
func (c *RoomController) Detail() {
	roomId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	roomManager := &models.RoomManager{}
	accommodationManager := &models.AccommodationManager{}

	// 获取房间信息
	room, err := roomManager.GetRoomById(roomId)
	if err != nil {
		beego.Error("获取房间信息失败:", err)
		c.Abort("404")
		return
	}

	// 获取当前住客
	accommodations, err := accommodationManager.GetActiveAccommodationsByRoom(roomId)
	if err != nil {
		beego.Error("获取住宿记录失败:", err)
		accommodations = []*models.Accommodation{}
	}

	// 获取可用床位
	availableBeds, err := accommodationManager.GetAvailableBeds(roomId)
	if err != nil {
		beego.Error("获取可用床位失败:", err)
		availableBeds = []string{}
	}

	c.Data["Room"] = room
	c.Data["Accommodations"] = accommodations
	c.Data["AvailableBeds"] = availableBeds
	c.Data["Title"] = "房间详情 - " + room.RoomNumber
	c.Data["PageName"] = "房间详情"
	c.TplName = "room/detail.html"
}

// 住客分配页面
func (c *RoomController) Assign() {
	roomId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	if c.Ctx.Input.Method() == "GET" {
		// 显示分配页面
		roomManager := &models.RoomManager{}
		accommodationManager := &models.AccommodationManager{}

		// 获取房间信息
		room, err := roomManager.GetRoomById(roomId)
		if err != nil {
			beego.Error("获取房间信息失败:", err)
			c.Abort("404")
			return
		}

		// 获取当前住客信息 - 通过住宿记录获取
		accommodations, err := accommodationManager.GetActiveAccommodationsByRoom(roomId)
		currentGuests := []*models.Guest{}
		if err == nil {
			for _, acc := range accommodations {
				if acc.Guest != nil {
					currentGuests = append(currentGuests, acc.Guest)
				}
			}
		}

		// 生成床位编号数组用于模板显示
		bedNumbers := make([]int, room.TotalBeds)
		for i := 0; i < room.TotalBeds; i++ {
			bedNumbers[i] = i
		}

		c.Data["Room"] = room
		c.Data["CurrentGuests"] = currentGuests
		c.Data["BedNumbers"] = bedNumbers
		c.Data["Title"] = "住客分配 - " + room.RoomNumber
		c.Data["PageName"] = "住客分配"
		c.TplName = "room/assign.html"

	} else if c.Ctx.Input.Method() == "POST" {
		// 处理分配请求
		var response map[string]interface{}

		// 获取表单数据
		guestName := c.GetString("guest_name")
		guestGender := c.GetString("guest_gender")
		guestPhone := c.GetString("guest_phone")
		guestIdCard := c.GetString("guest_idcard")

		// 验证必填字段
		if guestName == "" || guestGender == "" || guestPhone == "" || guestIdCard == "" {
			response = map[string]interface{}{
				"success": false,
				"message": "请填写完整的住客信息",
			}
			c.Data["json"] = response
			c.ServeJSON()
			return
		}

		// 性别校验
		genderService := models.NewGenderValidationService()
		validationResult := genderService.ValidateGenderCompatibility(roomId, guestGender)

		if !validationResult.IsValid && validationResult.RuleType == "strict" {
			response = map[string]interface{}{
				"success": false,
				"message": validationResult.Message,
				"type":    "gender_conflict",
			}
			c.Data["json"] = response
			c.ServeJSON()
			return
		}

		// 创建或获取住客
		guestManager := &models.GuestManager{}
		var guest *models.Guest

		// 检查住客是否已存在
		exists, existingGuest, _ := guestManager.GuestExists(guestPhone, guestIdCard)
		if exists {
			guest = existingGuest
		} else {
			// 创建新住客
			guest = &models.Guest{
				Name:   guestName,
				Gender: guestGender,
				Phone:  guestPhone,
				IdCard: guestIdCard,
			}
			err := guestManager.CreateGuest(guest)
			if err != nil {
				beego.Error("创建住客失败:", err)
				response = map[string]interface{}{
					"success": false,
					"message": "创建住客信息失败",
				}
				c.Data["json"] = response
				c.ServeJSON()
				return
			}
		}

		// 创建订单
		orderManager := &models.OrderManager{}
		order := &models.Order{
			OrderNumber: orderManager.GenerateOrderNumber(),
			RoomId:      roomId,
			GuestId:     guest.Id,
			OrderStatus: "checked_in",
			BedCount:    1,
		}

		err := orderManager.CreateOrder(order)
		if err != nil {
			beego.Error("创建订单失败:", err)
			response = map[string]interface{}{
				"success": false,
				"message": "创建订单失败",
			}
			c.Data["json"] = response
			c.ServeJSON()
			return
		}

		// 获取房间信息用于更新
		roomManager := &models.RoomManager{}
		room, err := roomManager.GetRoomById(roomId)
		if err != nil {
			beego.Error("获取房间信息失败:", err)
			response = map[string]interface{}{
				"success": false,
				"message": "获取房间信息失败",
			}
			c.Data["json"] = response
			c.ServeJSON()
			return
		}

		// 创建住宿记录
		accommodationManager := &models.AccommodationManager{}
		bedNumber := "床位" + strconv.Itoa(int(room.OccupiedBeds+1))
		accommodation := &models.Accommodation{
			OrderId:             order.Id,
			RoomId:              roomId,
			GuestId:             guest.Id,
			BedNumber:           bedNumber,
			CheckInTime:         time.Now(),
			AccommodationStatus: "active",
		}

		err = accommodationManager.CreateAccommodation(accommodation)
		if err != nil {
			beego.Error("创建住宿记录失败:", err)
			response = map[string]interface{}{
				"success": false,
				"message": "创建住宿记录失败",
			}
			c.Data["json"] = response
			c.ServeJSON()
			return
		}

		// 更新房间状态
		err = roomManager.UpdateRoomStatus(roomId, room.OccupiedBeds+1, guestGender)
		if err != nil {
			beego.Error("更新房间状态失败:", err)
		}

		// 记录操作日志
		logManager := &models.OperationLogManager{}
		logManager.LogSuccess(
			"系统管理员",
			"room_assign",
			"accommodation",
			accommodation.Id,
			"成功分配住客到房间",
			c.Ctx.Input.IP(),
		)

		response = map[string]interface{}{
			"success": true,
			"message": "住客分配成功",
			"data": map[string]interface{}{
				"guest_name":  guest.Name,
				"room_number": room.RoomNumber,
				"bed_number":  bedNumber,
			},
		}

		// 如果是性别冲突但允许覆盖的情况，记录警告
		if !validationResult.IsValid && validationResult.RuleType == "flexible" {
			logManager.LogWarning(
				"系统管理员",
				"gender_check",
				"accommodation",
				accommodation.Id,
				"管理员覆盖性别限制",
				validationResult.Message,
				c.Ctx.Input.IP(),
			)
			response["warning"] = validationResult.Message
		}

		c.Data["json"] = response
		c.ServeJSON()
	}
}
