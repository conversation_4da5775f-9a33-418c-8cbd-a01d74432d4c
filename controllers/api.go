package controllers

import (
	"bed_sharing_system/models"
	"strconv"

	"github.com/astaxie/beego"
)

// API控制器
type ApiController struct {
	beego.Controller
}

// 性别校验API
func (c *ApiController) ValidateGender() {
	roomId, _ := strconv.Atoi(c.GetString("room_id"))
	guestGender := c.GetString("guest_gender")

	if roomId == 0 || guestGender == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "参数不完整",
		}
		c.ServeJSON()
		return
	}

	// 性别校验
	genderService := models.NewGenderValidationService()
	result := genderService.ValidateGenderCompatibility(roomId, guestGender)

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    result,
	}
	c.ServeJSON()
}

// 获取可用床位API
func (c *ApiController) GetAvailableBeds() {
	roomId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	accommodationManager := &models.AccommodationManager{}
	beds, err := accommodationManager.GetAvailableBeds(roomId)

	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取可用床位失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    beds,
		}
	}

	c.ServeJSON()
}

// 获取仪表板统计数据API
func (c *ApiController) GetDashboardStats() {
	roomManager := &models.RoomManager{}
	orderManager := &models.OrderManager{}
	accommodationManager := &models.AccommodationManager{}

	// 获取房间统计
	roomStats, err := roomManager.GetRoomStats()
	if err != nil {
		beego.Error("获取房间统计失败:", err)
		roomStats = &models.RoomStats{}
	}

	// 获取订单统计
	orderStats, err := orderManager.GetOrderStats()
	if err != nil {
		beego.Error("获取订单统计失败:", err)
		orderStats = &models.OrderStats{}
	}

	// 获取住宿统计
	accommodationStats, err := accommodationManager.GetAccommodationStats()
	if err != nil {
		beego.Error("获取住宿统计失败:", err)
		accommodationStats = &models.AccommodationStats{}
	}

	// 组合统计数据
	dashboardStats := map[string]interface{}{
		"room_stats":          roomStats,
		"order_stats":         orderStats,
		"accommodation_stats": accommodationStats,
		"summary": map[string]interface{}{
			"total_rooms":     roomStats.TotalRooms,
			"occupied_rooms":  roomStats.OccupiedRooms,
			"available_rooms": roomStats.AvailableRooms,
			"total_beds":      roomStats.TotalBeds,
			"occupied_beds":   roomStats.OccupiedBeds,
			"available_beds":  roomStats.TotalBeds - roomStats.OccupiedBeds,
			"occupancy_rate":  float64(roomStats.OccupiedBeds) / float64(roomStats.TotalBeds) * 100,
			"active_orders":   orderStats.ActiveOrders,
			"total_revenue":   orderStats.TotalRevenue,
			"pending_revenue": orderStats.PendingRevenue,
		},
	}

	c.Data["json"] = map[string]interface{}{
		"success": true,
		"data":    dashboardStats,
	}
	c.ServeJSON()
}
