package controllers

import (
	"bed_sharing_system/models"
	"strconv"

	"github.com/astaxie/beego"
)

// 订单控制器
type OrderController struct {
	beego.Controller
}

// 订单列表
func (c *OrderController) List() {
	if c.Ctx.Input.Method() == "GET" && c.Ctx.Input.Header("Accept") != "application/json" {
		// 显示订单管理页面
		c.Data["Title"] = "订单管理"
		c.Data["PageName"] = "订单管理"
		c.TplName = "order/index.html"
		return
	}

	// API请求 - 返回JSON数据
	orderManager := &models.OrderManager{}

	// 获取筛选参数
	status := c.GetString("status")
	roomId, _ := strconv.Atoi(c.GetString("room_id"))

	var orders []*models.Order
	var err error

	if status != "" {
		orders, err = orderManager.GetOrdersByStatus(status)
	} else if roomId > 0 {
		orders, err = orderManager.GetOrdersByRoom(roomId)
	} else {
		orders, err = orderManager.GetAllOrders()
	}

	if err != nil {
		beego.Error("获取订单列表失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取订单列表失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"data":    orders,
		}
	}

	c.ServeJSON()
}

// 合并订单
func (c *OrderController) Merge() {
	var requestData struct {
		OrderIds []int `json:"order_ids"`
	}

	if err := c.ParseForm(&requestData); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求参数解析失败",
		}
		c.ServeJSON()
		return
	}

	if len(requestData.OrderIds) < 2 {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "至少需要选择2个订单进行合并",
		}
		c.ServeJSON()
		return
	}

	orderManager := &models.OrderManager{}
	// 使用第一个订单作为父订单，其余作为子订单
	parentOrderId := requestData.OrderIds[0]
	childOrderIds := requestData.OrderIds[1:]
	err := orderManager.MergeOrders(parentOrderId, childOrderIds)
	if err != nil {
		beego.Error("合并订单失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "合并订单失败: " + err.Error(),
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "订单合并成功",
		}
	}

	c.ServeJSON()
}

// 拆分订单
func (c *OrderController) Split() {
	orderId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	orderManager := &models.OrderManager{}
	// 创建空的拆分数据
	splitData := make(map[string]interface{})
	err := orderManager.SplitOrder(orderId, splitData)
	if err != nil {
		beego.Error("拆分订单失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "拆分订单失败: " + err.Error(),
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "订单拆分成功",
		}
	}

	c.ServeJSON()
}

// 更新订单状态
func (c *OrderController) UpdateStatus() {
	orderId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	var requestData struct {
		Status string `json:"status"`
	}

	if err := c.ParseForm(&requestData); err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请求参数解析失败",
		}
		c.ServeJSON()
		return
	}

	orderManager := &models.OrderManager{}
	order, err := orderManager.GetOrderById(orderId)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "订单不存在",
		}
		c.ServeJSON()
		return
	}

	order.OrderStatus = requestData.Status
	err = orderManager.UpdateOrder(order)
	if err != nil {
		beego.Error("更新订单状态失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "更新订单状态失败",
		}
	} else {
		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "订单状态更新成功",
			"data":    order,
		}
	}

	c.ServeJSON()
}

// 新建订单页面
func (c *OrderController) New() {
	if c.Ctx.Input.Method() == "GET" {
		c.Data["Title"] = "新建订单"
		c.Data["PageName"] = "订单管理"
		c.TplName = "order/new.html"
	} else if c.Ctx.Input.Method() == "POST" {
		// 处理新建订单请求
		orderManager := &models.OrderManager{}

		roomId, _ := strconv.Atoi(c.GetString("room_id"))
		guestId, _ := strconv.Atoi(c.GetString("guest_id"))
		bedCount, _ := strconv.Atoi(c.GetString("bed_count", "1"))

		order := &models.Order{
			OrderNumber: orderManager.GenerateOrderNumber(),
			RoomId:      roomId,
			GuestId:     guestId,
			OrderStatus: "pending",
			BedCount:    bedCount,
		}

		// 验证必填字段
		if roomId == 0 || guestId == 0 {
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "房间和住客为必选项",
			}
			c.ServeJSON()
			return
		}

		err := orderManager.CreateOrder(order)
		if err != nil {
			beego.Error("创建订单失败:", err)
			c.Data["json"] = map[string]interface{}{
				"success": false,
				"message": "创建订单失败",
			}
		} else {
			c.Data["json"] = map[string]interface{}{
				"success": true,
				"message": "订单创建成功",
				"data":    order,
			}
		}

		c.ServeJSON()
	}
}

// 订单详情
func (c *OrderController) Detail() {
	orderId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	orderManager := &models.OrderManager{}
	accommodationManager := &models.AccommodationManager{}

	// 获取订单信息
	order, err := orderManager.GetOrderById(orderId)
	if err != nil {
		beego.Error("获取订单信息失败:", err)
		c.Abort("404")
		return
	}

	// 获取住宿记录
	accommodations, err := accommodationManager.GetAccommodationsByOrder(orderId)
	if err != nil {
		beego.Error("获取住宿记录失败:", err)
		accommodations = []*models.Accommodation{}
	}

	c.Data["Order"] = order
	c.Data["Accommodations"] = accommodations
	c.Data["Title"] = "订单详情 - " + order.OrderNumber
	c.Data["PageName"] = "订单管理"
	c.TplName = "order/detail.html"
}

// 办理入住
func (c *OrderController) CheckIn() {
	orderId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))
	bedNumber := c.GetString("bed_number")

	if bedNumber == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "请选择床位",
		}
		c.ServeJSON()
		return
	}

	orderManager := &models.OrderManager{}
	accommodationManager := &models.AccommodationManager{}

	// 获取订单信息
	order, err := orderManager.GetOrderById(orderId)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "订单不存在",
		}
		c.ServeJSON()
		return
	}

	// 办理入住
	accommodation, err := accommodationManager.CheckIn(orderId, order.RoomId, order.GuestId, bedNumber)
	if err != nil {
		beego.Error("办理入住失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "办理入住失败",
		}
	} else {
		// 更新订单状态
		order.OrderStatus = "checked_in"
		orderManager.UpdateOrder(order)

		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "入住成功",
			"data":    accommodation,
		}
	}

	c.ServeJSON()
}

// 办理退房
func (c *OrderController) CheckOut() {
	orderId, _ := strconv.Atoi(c.Ctx.Input.Param(":id"))

	accommodationManager := &models.AccommodationManager{}
	orderManager := &models.OrderManager{}

	// 获取订单信息
	order, err := orderManager.GetOrderById(orderId)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "订单不存在",
		}
		c.ServeJSON()
		return
	}

	// 办理退房
	err = accommodationManager.CheckOut(orderId)
	if err != nil {
		beego.Error("办理退房失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "办理退房失败",
		}
	} else {
		// 更新订单状态
		order.OrderStatus = "checked_out"
		orderManager.UpdateOrder(order)

		c.Data["json"] = map[string]interface{}{
			"success": true,
			"message": "退房成功",
		}
	}

	c.ServeJSON()
}
