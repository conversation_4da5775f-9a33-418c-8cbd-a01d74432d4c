package controllers

import (
	"bed_sharing_system/models"

	"github.com/astaxie/beego"
)

// 住客分配控制器
type AssignmentController struct {
	beego.Controller
}

// 智能分配住客到房间
func (c *AssignmentController) SmartAssign() {
	// 获取表单数据
	guestName := c.GetString("guest_name")
	guestGender := c.GetString("guest_gender")
	guestPhone := c.GetString("guest_phone")
	guestIdCard := c.GetString("guest_idcard")
	stayNights, _ := c.GetInt("stay_nights", 1)

	// 验证必填字段
	if guestName == "" || guestGender == "" {
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "住客姓名和性别为必填项",
		}
		c.ServeJSON()
		return
	}

	// 获取适合的房间
	roomManager := &models.RoomManager{}
	availableRooms, err := roomManager.GetAvailableRoomsByGender(guestGender)
	if err != nil {
		beego.Error("获取可用房间失败:", err)
		c.Data["json"] = map[string]interface{}{
			"success": false,
			"message": "获取可用房间失败",
		}
		c.ServeJSON()
		return
	}

	// 如果没有可用房间，返回警告
	if len(availableRooms) == 0 {
		c.Data["json"] = map[string]interface{}{
			"success":      false,
			"message":      "暂无适合的房间",
			"warning_type": "no_available_rooms",
			"warning_msg":  "当前没有适合该性别的空闲房间，请稍后再试",
		}
		c.ServeJSON()
		return
	}

	// 选择第一个可用房间进行分配
	selectedRoom := availableRooms[0]
	c.processRoomAssignment(selectedRoom, guestName, guestGender, guestPhone, guestIdCard, stayNights)
}

// Test 显示分配测试页面
func (c *AssignmentController) Test() {
	c.TplName = "assignment/test.html"
}
