package middleware

import (
	"bed_sharing_system/models"
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/context"
)

// 性别校验中间件
type GenderValidationMiddleware struct {
	genderService *models.GenderValidationService
}

// 创建性别校验中间件实例
func NewGenderValidationMiddleware() *GenderValidationMiddleware {
	return &GenderValidationMiddleware{
		genderService: models.NewGenderValidationService(),
	}
}

// 中间件处理函数
func (m *GenderValidationMiddleware) ValidateGender(ctx *context.Context) {
	// 只对房间分配相关的POST请求进行校验
	if ctx.Request.Method != "POST" {
		return
	}

	// 检查是否是房间分配相关的路径
	if !m.isRoomAssignmentPath(ctx.Request.URL.Path) {
		return
	}

	// 解析请求参数
	roomId, guestGender, err := m.parseRequestParams(ctx)
	if err != nil {
		beego.Error("解析请求参数失败:", err)
		return
	}

	// 如果参数不完整，跳过校验
	if roomId == 0 || guestGender == "" {
		return
	}

	// 执行性别校验
	result := m.genderService.ValidateGenderCompatibility(roomId, guestGender)

	// 如果是严格模式且校验失败，直接拒绝请求
	if !result.IsValid && result.RuleType == "strict" {
		m.rejectRequest(ctx, result.Message)
		return
	}

	// 如果是灵活模式且有冲突，在上下文中添加警告信息
	if !result.IsValid && result.RuleType == "flexible" {
		ctx.Input.SetData("gender_warning", result.Message)
		ctx.Input.SetData("gender_conflict", true)
	}

	// 记录校验日志
	m.logValidation(ctx, roomId, guestGender, result)
}

// 检查是否是房间分配相关的路径
func (m *GenderValidationMiddleware) isRoomAssignmentPath(path string) bool {
	assignmentPaths := []string{
		"/rooms/",
		"/api/rooms/",
	}

	for _, assignPath := range assignmentPaths {
		if strings.Contains(path, assignPath) && strings.Contains(path, "assign") {
			return true
		}
	}

	return false
}

// 解析请求参数
func (m *GenderValidationMiddleware) parseRequestParams(ctx *context.Context) (int, string, error) {
	var roomId int
	var guestGender string
	var err error

	// 从URL路径中提取房间ID
	pathParts := strings.Split(ctx.Request.URL.Path, "/")
	for i, part := range pathParts {
		if part == "rooms" && i+1 < len(pathParts) {
			roomId, _ = strconv.Atoi(pathParts[i+1])
			break
		}
	}

	// 从表单数据中获取性别信息
	if ctx.Request.Form != nil {
		guestGender = ctx.Request.Form.Get("guest_gender")
	}

	// 如果表单数据为空，尝试从JSON数据中获取
	if guestGender == "" {
		var requestData map[string]interface{}
		if ctx.Request.Body != nil {
			decoder := json.NewDecoder(ctx.Request.Body)
			if err := decoder.Decode(&requestData); err == nil {
				if gender, ok := requestData["guest_gender"].(string); ok {
					guestGender = gender
				}
			}
		}
	}

	return roomId, guestGender, err
}

// 拒绝请求
func (m *GenderValidationMiddleware) rejectRequest(ctx *context.Context, message string) {
	response := map[string]interface{}{
		"success": false,
		"message": message,
		"type":    "gender_conflict",
		"code":    "GENDER_VALIDATION_FAILED",
	}

	ctx.Output.Header("Content-Type", "application/json")
	ctx.Output.SetStatus(http.StatusBadRequest)

	jsonData, _ := json.Marshal(response)
	ctx.Output.Body(jsonData)
}

// 记录校验日志
func (m *GenderValidationMiddleware) logValidation(ctx *context.Context, roomId int, guestGender string, result *models.GenderValidationResult) {
	logManager := &models.OperationLogManager{}

	logType := "info"
	if !result.IsValid {
		if result.RuleType == "strict" {
			logType = "error"
		} else {
			logType = "warning"
		}
	}

	logData := map[string]interface{}{
		"room_id":      roomId,
		"guest_gender": guestGender,
		"rule_type":    result.RuleType,
		"is_valid":     result.IsValid,
		"message":      result.Message,
	}

	switch logType {
	case "error":
		logManager.LogError(
			"系统中间件",
			"gender_validation",
			"room",
			roomId,
			"性别校验失败",
			result.Message,
			ctx.Request.RemoteAddr,
		)
	case "warning":
		logManager.LogWarning(
			"系统中间件",
			"gender_validation",
			"room",
			roomId,
			"性别校验警告",
			result.Message,
			ctx.Request.RemoteAddr,
		)
	default:
		logManager.LogInfo(
			"系统中间件",
			"gender_validation",
			"性别校验通过",
			roomId,
			result.Message,
			ctx.Request.RemoteAddr,
		)
	}

	beego.Info("性别校验中间件:", logData)
}

// 注册中间件到Beego
func RegisterGenderValidationMiddleware() {
	middleware := NewGenderValidationMiddleware()
	beego.InsertFilter("/*", beego.BeforeRouter, middleware.ValidateGender)
}

// 批量校验中间件 - 用于多人分配场景
type BatchGenderValidationMiddleware struct {
	genderService *models.GenderValidationService
}

// 创建批量校验中间件实例
func NewBatchGenderValidationMiddleware() *BatchGenderValidationMiddleware {
	return &BatchGenderValidationMiddleware{
		genderService: models.NewGenderValidationService(),
	}
}

// 批量校验处理函数
func (m *BatchGenderValidationMiddleware) ValidateBatchGender(ctx *context.Context) {
	// 只对批量分配相关的POST请求进行校验
	if ctx.Request.Method != "POST" {
		return
	}

	// 检查是否是批量分配路径
	if !strings.Contains(ctx.Request.URL.Path, "batch") || !strings.Contains(ctx.Request.URL.Path, "assign") {
		return
	}

	// 解析批量请求参数
	assignments, err := m.parseBatchRequestParams(ctx)
	if err != nil {
		beego.Error("解析批量请求参数失败:", err)
		return
	}

	// 执行批量校验
	results := m.genderService.ValidateBatchGenderCompatibility(assignments)

	// 检查是否有严格模式的冲突
	hasStrictConflict := false
	var conflictMessages []string

	for _, result := range results {
		if !result.IsValid && result.RuleType == "strict" {
			hasStrictConflict = true
			conflictMessages = append(conflictMessages, result.Message)
		}
	}

	// 如果有严格冲突，拒绝整个批量请求
	if hasStrictConflict {
		m.rejectBatchRequest(ctx, conflictMessages)
		return
	}

	// 将校验结果添加到上下文中
	ctx.Input.SetData("batch_validation_results", results)

	// 记录批量校验日志
	m.logBatchValidation(ctx, assignments, results)
}

// 解析批量请求参数
func (m *BatchGenderValidationMiddleware) parseBatchRequestParams(ctx *context.Context) ([]models.GenderAssignment, error) {
	var assignments []models.GenderAssignment
	var requestData map[string]interface{}

	if ctx.Request.Body != nil {
		decoder := json.NewDecoder(ctx.Request.Body)
		if err := decoder.Decode(&requestData); err != nil {
			return assignments, err
		}

		if assignmentsData, ok := requestData["assignments"].([]interface{}); ok {
			for _, assignmentData := range assignmentsData {
				if assignment, ok := assignmentData.(map[string]interface{}); ok {
					roomId, _ := strconv.Atoi(assignment["room_id"].(string))
					guestGender, _ := assignment["guest_gender"].(string)

					assignments = append(assignments, models.GenderAssignment{
						RoomId:      roomId,
						GuestGender: guestGender,
					})
				}
			}
		}
	}

	return assignments, nil
}

// 拒绝批量请求
func (m *BatchGenderValidationMiddleware) rejectBatchRequest(ctx *context.Context, messages []string) {
	response := map[string]interface{}{
		"success":   false,
		"message":   "批量分配中存在性别冲突",
		"type":      "batch_gender_conflict",
		"code":      "BATCH_GENDER_VALIDATION_FAILED",
		"conflicts": messages,
	}

	ctx.Output.Header("Content-Type", "application/json")
	ctx.Output.SetStatus(http.StatusBadRequest)

	jsonData, _ := json.Marshal(response)
	ctx.Output.Body(jsonData)
}

// 记录批量校验日志
func (m *BatchGenderValidationMiddleware) logBatchValidation(ctx *context.Context, assignments []models.GenderAssignment, results []*models.GenderValidationResult) {
	logManager := &models.OperationLogManager{}

	conflictCount := 0
	for _, result := range results {
		if !result.IsValid {
			conflictCount++
		}
	}

	logData := map[string]interface{}{
		"assignment_count": len(assignments),
		"conflict_count":   conflictCount,
		"results":          results,
	}

	if conflictCount > 0 {
		logManager.LogWarning(
			"系统中间件",
			"batch_gender_validation",
			"system",
			0,
			"批量分配中发现性别冲突",
			"批量性别校验发现冲突",
			ctx.Request.RemoteAddr,
		)
	} else {
		logManager.LogInfo(
			"系统中间件",
			"batch_gender_validation",
			"system",
			0,
			"所有分配均通过性别校验",
			ctx.Request.RemoteAddr,
		)
	}

	beego.Info("批量性别校验中间件:", logData)
}

// 注册批量校验中间件到Beego
func RegisterBatchGenderValidationMiddleware() {
	middleware := NewBatchGenderValidationMiddleware()
	beego.InsertFilter("/api/*/batch/*", beego.BeforeRouter, middleware.ValidateBatchGender)
}
