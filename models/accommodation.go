package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 住宿记录模型
type Accommodation struct {
	Id                  int       `orm:"auto;pk" json:"id"`
	OrderId             int       `json:"order_id"`
	RoomId              int       `json:"room_id"`
	GuestId             int       `json:"guest_id"`
	BedNumber           string    `orm:"size(10);null" json:"bed_number"`
	CheckInTime         time.Time `orm:"type(datetime);null" json:"check_in_time"`
	CheckOutTime        time.Time `orm:"type(datetime);null" json:"check_out_time"`
	AccommodationStatus string    `orm:"size(20);default(active)" json:"accommodation_status"` // active, completed, cancelled
	CreatedAt           time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt           time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`

	// 关联字段 - 手动加载，避免ORM自动字段冲突
	Order *Order `orm:"-" json:"order,omitempty"`
	Room  *Room  `orm:"-" json:"room,omitempty"`
	Guest *Guest `orm:"-" json:"guest,omitempty"`
}

// 表名
func (a *Accommodation) TableName() string {
	return "accommodations"
}

// 住宿管理器
type AccommodationManager struct{}

// 创建住宿记录
func (am *AccommodationManager) CreateAccommodation(accommodation *Accommodation) error {
	o := orm.NewOrm()
	_, err := o.Insert(accommodation)
	return err
}

// 根据ID获取住宿记录
func (am *AccommodationManager) GetAccommodationById(id int) (*Accommodation, error) {
	o := orm.NewOrm()
	accommodation := &Accommodation{Id: id}
	err := o.Read(accommodation)
	if err == nil {
		o.LoadRelated(accommodation, "Order")
		o.LoadRelated(accommodation, "Room")
		o.LoadRelated(accommodation, "Guest")
	}
	return accommodation, err
}

// 获取房间的所有住宿记录
func (am *AccommodationManager) GetAccommodationsByRoom(roomId int) ([]*Accommodation, error) {
	o := orm.NewOrm()
	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").
		Filter("room_id", roomId).
		OrderBy("-created_at").
		All(&accommodations)

	for _, acc := range accommodations {
		o.LoadRelated(acc, "Order")
		o.LoadRelated(acc, "Room")
		o.LoadRelated(acc, "Guest")
	}

	return accommodations, err
}

// 获取房间的活跃住宿记录
func (am *AccommodationManager) GetActiveAccommodationsByRoom(roomId int) ([]*Accommodation, error) {
	o := orm.NewOrm()
	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").
		Filter("room_id", roomId).
		Filter("accommodation_status", "active").
		OrderBy("-created_at").
		All(&accommodations)

	for _, acc := range accommodations {
		o.LoadRelated(acc, "Order")
		o.LoadRelated(acc, "Room")
		o.LoadRelated(acc, "Guest")
	}

	return accommodations, err
}

// 获取住客的住宿记录
func (am *AccommodationManager) GetAccommodationsByGuest(guestId int) ([]*Accommodation, error) {
	o := orm.NewOrm()
	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").
		Filter("guest_id", guestId).
		OrderBy("-created_at").
		All(&accommodations)

	for _, acc := range accommodations {
		o.LoadRelated(acc, "Order")
		o.LoadRelated(acc, "Room")
		o.LoadRelated(acc, "Guest")
	}

	return accommodations, err
}

// 获取住客当前住宿记录
func (am *AccommodationManager) GetCurrentAccommodationByGuest(guestId int) (*Accommodation, error) {
	o := orm.NewOrm()
	accommodation := &Accommodation{}
	err := o.QueryTable("accommodations").
		Filter("guest_id", guestId).
		Filter("accommodation_status", "active").
		One(accommodation)

	if err == nil {
		o.LoadRelated(accommodation, "Order")
		o.LoadRelated(accommodation, "Room")
		o.LoadRelated(accommodation, "Guest")
	}

	return accommodation, err
}

// 办理入住
func (am *AccommodationManager) CheckIn(orderId, roomId, guestId int, bedNumber string) (*Accommodation, error) {
	o := orm.NewOrm()

	// 检查是否已有活跃住宿记录
	existing := &Accommodation{}
	err := o.QueryTable("accommodations").
		Filter("guest_id", guestId).
		Filter("accommodation_status", "active").
		One(existing)

	if err == nil {
		return nil, orm.ErrNoRows // 住客已有活跃住宿记录
	}

	// 创建新的住宿记录
	accommodation := &Accommodation{
		OrderId:             orderId,
		RoomId:              roomId,
		GuestId:             guestId,
		BedNumber:           bedNumber,
		CheckInTime:         time.Now(),
		AccommodationStatus: "active",
	}

	_, err = o.Insert(accommodation)
	if err != nil {
		return nil, err
	}

	// 加载关联数据
	o.LoadRelated(accommodation, "Order")
	o.LoadRelated(accommodation, "Room")
	o.LoadRelated(accommodation, "Guest")

	return accommodation, nil
}

// 办理退房
func (am *AccommodationManager) CheckOut(accommodationId int) error {
	o := orm.NewOrm()

	accommodation := &Accommodation{Id: accommodationId}
	if err := o.Read(accommodation); err != nil {
		return err
	}

	accommodation.CheckOutTime = time.Now()
	accommodation.AccommodationStatus = "completed"

	_, err := o.Update(accommodation)
	return err
}

// 批量退房（按房间）
func (am *AccommodationManager) BatchCheckOutByRoom(roomId int) error {
	o := orm.NewOrm()

	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").
		Filter("room_id", roomId).
		Filter("accommodation_status", "active").
		All(&accommodations)

	if err != nil {
		return err
	}

	// 开始事务
	err = o.Begin()
	if err != nil {
		return err
	}

	for _, acc := range accommodations {
		acc.CheckOutTime = time.Now()
		acc.AccommodationStatus = "completed"

		if _, err := o.Update(acc); err != nil {
			o.Rollback()
			return err
		}
	}

	return o.Commit()
}

// 获取可用床位
func (am *AccommodationManager) GetAvailableBeds(roomId int) ([]string, error) {
	o := orm.NewOrm()

	// 获取房间信息
	room := &Room{Id: roomId}
	if err := o.Read(room); err != nil {
		return nil, err
	}

	// 获取已占用的床位
	var occupiedBeds orm.ParamsList
	o.QueryTable("accommodations").
		Filter("room_id", roomId).
		Filter("accommodation_status", "active").
		ValuesFlat(&occupiedBeds, "bed_number")

	// 生成所有床位号
	allBeds := make([]string, room.TotalBeds)
	for i := 0; i < room.TotalBeds; i++ {
		allBeds[i] = string(rune('A' + i)) // A, B, C, D...
	}

	// 过滤出可用床位
	var availableBeds []string
	for _, bed := range allBeds {
		occupied := false
		for _, occupiedBed := range occupiedBeds {
			if bed == occupiedBed {
				occupied = true
				break
			}
		}
		if !occupied {
			availableBeds = append(availableBeds, bed)
		}
	}

	return availableBeds, nil
}

// GetAccommodationsByOrder 根据订单ID获取住宿记录
func (am *AccommodationManager) GetAccommodationsByOrder(orderId int) ([]*Accommodation, error) {
	o := orm.NewOrm()
	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").Filter("order_id", orderId).All(&accommodations)

	// 加载关联数据
	for _, acc := range accommodations {
		o.LoadRelated(acc, "Room")
		o.LoadRelated(acc, "Guest")
		o.LoadRelated(acc, "Order")
	}

	return accommodations, err
}

// 住宿统计
type AccommodationStats struct {
	TotalAccommodations     int     `json:"total_accommodations"`
	ActiveAccommodations    int     `json:"active_accommodations"`
	CompletedAccommodations int     `json:"completed_accommodations"`
	AverageStayDuration     float64 `json:"average_stay_duration"`
}

// 获取住宿统计
func (am *AccommodationManager) GetAccommodationStats() (*AccommodationStats, error) {
	o := orm.NewOrm()
	stats := &AccommodationStats{}

	// 总住宿记录数
	total, _ := o.QueryTable("accommodations").Count()
	stats.TotalAccommodations = int(total)

	// 活跃住宿记录数
	active, _ := o.QueryTable("accommodations").Filter("accommodation_status", "active").Count()
	stats.ActiveAccommodations = int(active)

	// 已完成住宿记录数
	completed, _ := o.QueryTable("accommodations").Filter("accommodation_status", "completed").Count()
	stats.CompletedAccommodations = int(completed)

	// 计算平均住宿时长（已完成的记录）
	var completedAccommodations []*Accommodation
	o.QueryTable("accommodations").
		Filter("accommodation_status", "completed").
		All(&completedAccommodations)

	if len(completedAccommodations) > 0 {
		totalDuration := 0.0
		for _, acc := range completedAccommodations {
			if !acc.CheckInTime.IsZero() && !acc.CheckOutTime.IsZero() {
				duration := acc.CheckOutTime.Sub(acc.CheckInTime).Hours() / 24
				totalDuration += duration
			}
		}
		stats.AverageStayDuration = totalDuration / float64(len(completedAccommodations))
	}

	return stats, nil
}

// GetOccupancyTrend 获取入住率趋势
func (am *AccommodationManager) GetOccupancyTrend(days int) ([]map[string]interface{}, error) {
	o := orm.NewOrm()
	var result []map[string]interface{}

	// 获取过去N天的入住数据
	for i := days - 1; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")

		var checkInCount int
		o.Raw("SELECT COUNT(*) FROM accommodations WHERE DATE(check_in_time) = ?", date).QueryRow(&checkInCount)

		var checkOutCount int
		o.Raw("SELECT COUNT(*) FROM accommodations WHERE DATE(check_out_time) = ?", date).QueryRow(&checkOutCount)

		var occupiedBeds int
		o.Raw("SELECT COUNT(*) FROM accommodations WHERE accommodation_status = 'active' AND DATE(check_in_time) <= ?", date).QueryRow(&occupiedBeds)

		result = append(result, map[string]interface{}{
			"date":          date,
			"check_ins":     checkInCount,
			"check_outs":    checkOutCount,
			"occupied_beds": occupiedBeds,
		})
	}

	return result, nil
}

// GetActiveAccommodations 获取活跃住宿记录
func (am *AccommodationManager) GetActiveAccommodations() ([]*Accommodation, error) {
	o := orm.NewOrm()
	var accommodations []*Accommodation
	_, err := o.QueryTable("accommodations").
		Filter("accommodation_status", "active").
		All(&accommodations)

	// 加载关联数据
	for _, acc := range accommodations {
		o.LoadRelated(acc, "Guest")
		o.LoadRelated(acc, "Room")
		o.LoadRelated(acc, "Order")
	}

	return accommodations, err
}

// GetActiveAccommodationCount 获取活跃住宿记录数量
func (am *AccommodationManager) GetActiveAccommodationCount() (int, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable("accommodations").
		Filter("accommodation_status", "active").
		Count()
	return int(count), err
}

// GetCheckInCountByDate 获取指定日期的入住数量
func (am *AccommodationManager) GetCheckInCountByDate(date string) (int, error) {
	o := orm.NewOrm()
	var count int
	err := o.Raw("SELECT COUNT(*) FROM accommodations WHERE DATE(check_in_time) = ?", date).QueryRow(&count)
	return count, err
}

// GetCheckOutCountByDate 获取指定日期的退房数量
func (am *AccommodationManager) GetCheckOutCountByDate(date string) (int, error) {
	o := orm.NewOrm()
	var count int
	err := o.Raw("SELECT COUNT(*) FROM accommodations WHERE DATE(check_out_time) = ?", date).QueryRow(&count)
	return count, err
}

// GetGenderDistribution 获取性别分布统计
func (am *AccommodationManager) GetGenderDistribution() (map[string]interface{}, error) {
	o := orm.NewOrm()

	var maleCount int
	o.Raw(`SELECT COUNT(*) FROM accommodations a 
			JOIN guests g ON a.guest_id = g.id 
			WHERE a.accommodation_status = 'active' AND g.gender = 'male'`).QueryRow(&maleCount)

	var femaleCount int
	o.Raw(`SELECT COUNT(*) FROM accommodations a 
			JOIN guests g ON a.guest_id = g.id 
			WHERE a.accommodation_status = 'active' AND g.gender = 'female'`).QueryRow(&femaleCount)

	result := map[string]interface{}{
		"male":   maleCount,
		"female": femaleCount,
		"total":  maleCount + femaleCount,
	}

	return result, nil
}
