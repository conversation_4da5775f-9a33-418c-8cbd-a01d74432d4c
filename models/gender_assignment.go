package models

// 性别分配结构体 - 用于批量校验
type GenderAssignment struct {
	RoomId      int    `json:"room_id"`
	GuestGender string `json:"guest_gender"`
	GuestName   string `json:"guest_name,omitempty"`
	BedNumber   string `json:"bed_number,omitempty"`
}

// 批量性别校验结果
type BatchGenderValidationResult struct {
	TotalAssignments  int                       `json:"total_assignments"`
	ValidAssignments  int                       `json:"valid_assignments"`
	ConflictCount     int                       `json:"conflict_count"`
	Results           []*GenderValidationResult `json:"results"`
	HasStrictConflict bool                      `json:"has_strict_conflict"`
	CanProceed        bool                      `json:"can_proceed"`
}

// 批量性别校验方法
func (g *GenderValidationService) ValidateBatchGenderCompatibility(assignments []GenderAssignment) []*GenderValidationResult {
	var results []*GenderValidationResult

	for _, assignment := range assignments {
		result := g.ValidateGenderCompatibility(assignment.RoomId, assignment.GuestGender)
		results = append(results, result)
	}

	return results
}

// 获取批量校验汇总结果
func (g *GenderValidationService) GetBatchValidationSummary(assignments []GenderAssignment) *BatchGenderValidationResult {
	results := g.ValidateBatchGenderCompatibility(assignments)

	summary := &BatchGenderValidationResult{
		TotalAssignments: len(assignments),
		Results:          results,
	}

	for _, result := range results {
		if result.IsValid {
			summary.ValidAssignments++
		} else {
			summary.ConflictCount++
			if result.RuleType == "strict" {
				summary.HasStrictConflict = true
			}
		}
	}

	// 如果没有严格冲突，可以继续执行
	summary.CanProceed = !summary.HasStrictConflict

	return summary
}

// 智能房间推荐 - 根据性别推荐最合适的房间
type RoomRecommendation struct {
	RoomId             int     `json:"room_id"`
	RoomNumber         string  `json:"room_number"`
	AvailableBeds      int     `json:"available_beds"`
	CurrentGender      string  `json:"current_gender"`
	CompatibilityScore float64 `json:"compatibility_score"`
	RecommendReason    string  `json:"recommend_reason"`
}

// 获取性别兼容的房间推荐
func (g *GenderValidationService) GetGenderCompatibleRooms(guestGender string, limit int) ([]*RoomRecommendation, error) {
	roomManager := &RoomManager{}

	// 获取所有可用房间
	availableRooms, err := roomManager.GetAvailableRooms()
	if err != nil {
		return nil, err
	}

	var recommendations []*RoomRecommendation

	for _, room := range availableRooms {
		// 计算兼容性评分
		score, reason := g.calculateCompatibilityScore(room, guestGender)

		if score > 0 {
			recommendation := &RoomRecommendation{
				RoomId:             room.Id,
				RoomNumber:         room.RoomNumber,
				AvailableBeds:      room.TotalBeds - room.OccupiedBeds,
				CurrentGender:      room.CurrentGender,
				CompatibilityScore: score,
				RecommendReason:    reason,
			}
			recommendations = append(recommendations, recommendation)
		}
	}

	// 按兼容性评分排序
	for i := 0; i < len(recommendations)-1; i++ {
		for j := i + 1; j < len(recommendations); j++ {
			if recommendations[i].CompatibilityScore < recommendations[j].CompatibilityScore {
				recommendations[i], recommendations[j] = recommendations[j], recommendations[i]
			}
		}
	}

	// 限制返回数量
	if limit > 0 && len(recommendations) > limit {
		recommendations = recommendations[:limit]
	}

	return recommendations, nil
}

// 计算房间兼容性评分
func (g *GenderValidationService) calculateCompatibilityScore(room *Room, guestGender string) (float64, string) {
	var score float64
	var reason string

	// 基础评分：可用床位数
	availableBeds := room.TotalBeds - room.OccupiedBeds
	score += float64(availableBeds) * 10

	// 性别兼容性评分
	if room.CurrentGender == "none" {
		// 空房间，完全兼容
		score += 100
		reason = "空房间，完全兼容"
	} else if room.CurrentGender == guestGender {
		// 同性别房间，高度兼容
		score += 80
		reason = "同性别房间，高度兼容"
	} else {
		// 异性别房间，需要检查规则
		result := g.ValidateGenderCompatibility(room.Id, guestGender)
		if result.IsValid {
			if result.RuleType == "flexible" {
				score += 30
				reason = "灵活模式允许，但有性别差异"
			}
		} else {
			score = 0
			reason = "性别冲突，不兼容"
		}
	}

	// 楼层偏好评分（可选）
	if room.FloorNumber <= 3 {
		score += 5 // 低楼层加分
	}

	return score, reason
}

// 自动分配最佳房间
func (g *GenderValidationService) AutoAssignBestRoom(guestGender string) (*RoomRecommendation, error) {
	recommendations, err := g.GetGenderCompatibleRooms(guestGender, 1)
	if err != nil {
		return nil, err
	}

	if len(recommendations) == 0 {
		return nil, WrapError("NO_COMPATIBLE_ROOM", "没有找到兼容的房间", nil)
	}

	return recommendations[0], nil
}
