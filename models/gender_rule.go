package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 性别校验规则模型
type GenderRule struct {
	Id          int       `orm:"auto;pk" json:"id"`
	RuleName    string    `orm:"size(100)" json:"rule_name"`
	RuleType    string    `orm:"size(20);default(strict)" json:"rule_type"` // strict, flexible, disabled
	Description string    `orm:"type(text);null" json:"description"`
	IsActive    bool      `orm:"default(true)" json:"is_active"`
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt   time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`
}

// 表名
func (gr *GenderRule) TableName() string {
	return "gender_rules"
}

// 性别校验规则管理器
type GenderRuleManager struct{}

// 获取当前活跃的性别规则
func (grm *GenderRuleManager) GetActiveRule() (*GenderRule, error) {
	o := orm.NewOrm()
	rule := &GenderRule{}
	err := o.QueryTable("gender_rules").Filter("is_active", true).One(rule)
	return rule, err
}

// 更新活跃规则
func (grm *GenderRuleManager) SetActiveRule(ruleId int) error {
	o := orm.NewOrm()

	// 开始事务
	err := o.Begin()
	if err != nil {
		return err
	}

	// 先将所有规则设为非活跃
	_, err = o.QueryTable("gender_rules").Update(orm.Params{"is_active": false})
	if err != nil {
		o.Rollback()
		return err
	}

	// 激活指定规则
	rule := &GenderRule{Id: ruleId}
	if err := o.Read(rule); err != nil {
		o.Rollback()
		return err
	}

	rule.IsActive = true
	_, err = o.Update(rule)
	if err != nil {
		o.Rollback()
		return err
	}

	return o.Commit()
}

// 获取所有规则
func (grm *GenderRuleManager) GetAllRules() ([]*GenderRule, error) {
	o := orm.NewOrm()
	var rules []*GenderRule
	_, err := o.QueryTable("gender_rules").All(&rules)
	return rules, err
}

// 性别校验结果
type GenderValidationResult struct {
	IsValid   bool     `json:"is_valid"`
	RuleType  string   `json:"rule_type"` // strict, flexible
	Message   string   `json:"message"`
	RoomId    int      `json:"room_id"`
	Conflicts []string `json:"conflicts,omitempty"`
}

// 性别校验服务
type GenderValidationService struct {
	ruleManager *GenderRuleManager
}

// 创建性别校验服务
func NewGenderValidationService() *GenderValidationService {
	return &GenderValidationService{
		ruleManager: &GenderRuleManager{},
	}
}

// 校验性别兼容性
func (gvs *GenderValidationService) ValidateGenderCompatibility(roomId int, guestGender string) *GenderValidationResult {
	// 获取当前活跃规则
	rule, err := gvs.ruleManager.GetActiveRule()
	if err != nil {
		return &GenderValidationResult{
			IsValid:  false,
			RuleType: "strict",
			Message:  "无法获取性别校验规则",
			RoomId:   roomId,
		}
	}

	// 如果规则被禁用，直接通过
	if rule.RuleType == "disabled" {
		return &GenderValidationResult{
			IsValid:  true,
			RuleType: rule.RuleType,
			Message:  "性别校验已禁用",
			RoomId:   roomId,
		}
	}

	// 获取房间信息
	o := orm.NewOrm()
	room := &Room{Id: roomId}
	if err := o.Read(room); err != nil {
		return &GenderValidationResult{
			IsValid:  false,
			RuleType: rule.RuleType,
			Message:  "房间不存在",
			RoomId:   roomId,
		}
	}

	// 检查房间是否已满
	if room.OccupiedBeds >= room.TotalBeds {
		return &GenderValidationResult{
			IsValid:  false,
			RuleType: rule.RuleType,
			Message:  "房间已满，无法分配",
			RoomId:   roomId,
		}
	}

	// 检查性别兼容性
	if room.CurrentGender == "none" || room.CurrentGender == guestGender {
		return &GenderValidationResult{
			IsValid:  true,
			RuleType: rule.RuleType,
			Message:  "性别校验通过",
			RoomId:   roomId,
		}
	}

	// 性别冲突处理
	if rule.RuleType == "strict" {
		return &GenderValidationResult{
			IsValid:  false,
			RuleType: rule.RuleType,
			Message:  "性别冲突：该房间已有" + getGenderText(room.CurrentGender) + "住客，不能分配" + getGenderText(guestGender) + "住客",
			RoomId:   roomId,
		}
	} else if rule.RuleType == "flexible" {
		return &GenderValidationResult{
			IsValid:  true,
			RuleType: rule.RuleType,
			Message:  "警告：性别冲突，但灵活模式允许分配",
			RoomId:   roomId,
		}
	}

	return &GenderValidationResult{
		IsValid:  false,
		RuleType: rule.RuleType,
		Message:  "未知的校验规则类型",
		RoomId:   roomId,
	}
}

// 批量校验多个住客
func (gvs *GenderValidationService) ValidateBatchGuestAssignment(roomId int, guestIds []int) map[int]*GenderValidationResult {
	results := make(map[int]*GenderValidationResult)

	o := orm.NewOrm()
	for _, guestId := range guestIds {
		guest := &Guest{Id: guestId}
		if err := o.Read(guest); err != nil {
			results[guestId] = &GenderValidationResult{
				IsValid:  false,
				RuleType: "strict",
				Message:  "住客不存在",
				RoomId:   roomId,
			}
			continue
		}

		result := gvs.ValidateGenderCompatibility(roomId, guest.Gender)
		results[guestId] = result
	}

	return results
}

// 获取性别文本
func getGenderText(gender string) string {
	switch gender {
	case "male":
		return "男性"
	case "female":
		return "女性"
	default:
		return "未知"
	}
}

// 创建默认性别规则
func (grm *GenderRuleManager) CreateDefaultRules() error {
	o := orm.NewOrm()

	// 检查是否已有规则
	count, _ := o.QueryTable("gender_rules").Count()
	if count > 0 {
		return nil // 已有规则，不需要创建
	}

	// 创建默认规则
	rules := []*GenderRule{
		{
			RuleName:    "严格模式",
			RuleType:    "strict",
			Description: "严格禁止不同性别住客共享房间",
			IsActive:    true,
		},
		{
			RuleName:    "灵活模式",
			RuleType:    "flexible",
			Description: "允许管理员覆盖性别限制",
			IsActive:    false,
		},
		{
			RuleName:    "禁用模式",
			RuleType:    "disabled",
			Description: "完全禁用性别校验",
			IsActive:    false,
		},
	}

	for _, rule := range rules {
		_, err := o.Insert(rule)
		if err != nil {
			return err
		}
	}

	return nil
}
