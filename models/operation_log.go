package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 操作日志模型
type OperationLog struct {
	Id              int       `orm:"auto;pk" json:"id"`
	Operator        string    `orm:"size(100);null" json:"operator"`
	OperationType   string    `orm:"size(50)" json:"operation_type"` // check_in, check_out, room_assign, order_merge, gender_check
	TargetType      string    `orm:"size(20)" json:"target_type"`    // room, guest, order, accommodation
	TargetId        int       `json:"target_id"`
	OperationDetail string    `orm:"type(text);null" json:"operation_detail"`
	Result          string    `orm:"size(20)" json:"result"` // success, failed, warning
	ErrorMessage    string    `orm:"type(text);null" json:"error_message"`
	IpAddress       string    `orm:"size(45);null" json:"ip_address"`
	CreatedAt       time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
}

// 表名
func (ol *OperationLog) TableName() string {
	return "operation_logs"
}

// 操作日志管理器
type OperationLogManager struct{}

// 记录操作日志
func (olm *OperationLogManager) LogOperation(operator, operationType, targetType string, targetId int, detail, result, errorMsg, ipAddress string) error {
	o := orm.NewOrm()

	log := &OperationLog{
		Operator:        operator,
		OperationType:   operationType,
		TargetType:      targetType,
		TargetId:        targetId,
		OperationDetail: detail,
		Result:          result,
		ErrorMessage:    errorMsg,
		IpAddress:       ipAddress,
	}

	_, err := o.Insert(log)
	return err
}

// 记录成功操作
func (olm *OperationLogManager) LogSuccess(operator, operationType, targetType string, targetId int, detail, ipAddress string) error {
	return olm.LogOperation(operator, operationType, targetType, targetId, detail, "success", "", ipAddress)
}

// 记录失败操作
func (olm *OperationLogManager) LogFailure(operator, operationType, targetType string, targetId int, detail, errorMsg, ipAddress string) error {
	return olm.LogOperation(operator, operationType, targetType, targetId, detail, "failed", errorMsg, ipAddress)
}

// 记录错误操作
func (olm *OperationLogManager) LogError(operator, operationType, targetType string, targetId int, detail, errorMsg, ipAddress string) error {
	return olm.LogOperation(operator, operationType, targetType, targetId, detail, "failed", errorMsg, ipAddress)
}

// 记录信息操作
func (olm *OperationLogManager) LogInfo(operator, operationType, targetType string, targetId int, detail, ipAddress string) error {
	return olm.LogOperation(operator, operationType, targetType, targetId, detail, "success", "", ipAddress)
}

// 记录警告操作
func (olm *OperationLogManager) LogWarning(operator, operationType, targetType string, targetId int, detail, warningMsg, ipAddress string) error {
	return olm.LogOperation(operator, operationType, targetType, targetId, detail, "warning", warningMsg, ipAddress)
}

// 获取操作日志（分页）
func (olm *OperationLogManager) GetLogsPaginated(page, pageSize int, operationType, result string) ([]*OperationLog, int64, error) {
	o := orm.NewOrm()
	var logs []*OperationLog

	qs := o.QueryTable("operation_logs")

	if operationType != "" {
		qs = qs.Filter("operation_type", operationType)
	}

	if result != "" {
		qs = qs.Filter("result", result)
	}

	offset := (page - 1) * pageSize
	_, err := qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&logs)

	total, _ := qs.Count()

	return logs, total, err
}

// 获取指定目标的操作日志
func (olm *OperationLogManager) GetLogsByTarget(targetType string, targetId int) ([]*OperationLog, error) {
	o := orm.NewOrm()
	var logs []*OperationLog

	_, err := o.QueryTable("operation_logs").
		Filter("target_type", targetType).
		Filter("target_id", targetId).
		OrderBy("-created_at").
		All(&logs)

	return logs, err
}

// 获取操作员的操作日志
func (olm *OperationLogManager) GetLogsByOperator(operator string, limit int) ([]*OperationLog, error) {
	o := orm.NewOrm()
	var logs []*OperationLog

	qs := o.QueryTable("operation_logs").
		Filter("operator", operator).
		OrderBy("-created_at")

	if limit > 0 {
		qs = qs.Limit(limit)
	}

	_, err := qs.All(&logs)
	return logs, err
}

// 获取今日操作统计
func (olm *OperationLogManager) GetTodayStats() (map[string]int, error) {
	o := orm.NewOrm()

	today := time.Now().Format("2006-01-02")

	stats := make(map[string]int)

	// 总操作数
	total, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Count()
	stats["total"] = int(total)

	// 成功操作数
	success, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("result", "success").
		Count()
	stats["success"] = int(success)

	// 失败操作数
	failed, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("result", "failed").
		Count()
	stats["failed"] = int(failed)

	// 警告操作数
	warning, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("result", "warning").
		Count()
	stats["warning"] = int(warning)

	// 各类型操作统计
	checkIn, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("operation_type", "check_in").
		Count()
	stats["check_in"] = int(checkIn)

	checkOut, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("operation_type", "check_out").
		Count()
	stats["check_out"] = int(checkOut)

	roomAssign, _ := o.QueryTable("operation_logs").
		Filter("created_at__gte", today+" 00:00:00").
		Filter("created_at__lt", today+" 23:59:59").
		Filter("operation_type", "room_assign").
		Count()
	stats["room_assign"] = int(roomAssign)

	return stats, nil
}

// 清理旧日志（保留指定天数）
func (olm *OperationLogManager) CleanOldLogs(keepDays int) (int64, error) {
	o := orm.NewOrm()

	cutoffDate := time.Now().AddDate(0, 0, -keepDays).Format("2006-01-02 15:04:05")

	num, err := o.QueryTable("operation_logs").
		Filter("created_at__lt", cutoffDate).
		Delete()

	return num, err
}

// 导出操作日志
func (olm *OperationLogManager) ExportLogs(startDate, endDate string, operationType string) ([]*OperationLog, error) {
	o := orm.NewOrm()
	var logs []*OperationLog

	qs := o.QueryTable("operation_logs")

	if startDate != "" {
		qs = qs.Filter("created_at__gte", startDate+" 00:00:00")
	}

	if endDate != "" {
		qs = qs.Filter("created_at__lte", endDate+" 23:59:59")
	}

	if operationType != "" {
		qs = qs.Filter("operation_type", operationType)
	}

	_, err := qs.OrderBy("-created_at").All(&logs)
	return logs, err
}
