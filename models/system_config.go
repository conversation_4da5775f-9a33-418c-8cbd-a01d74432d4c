package models

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/astaxie/beego/orm"
)

// 系统配置模型
type SystemConfig struct {
	Id          int       `orm:"auto;pk" json:"id"`
	ConfigKey   string    `orm:"size(100);unique" json:"config_key"`
	ConfigValue string    `orm:"type(text);null" json:"config_value"`
	ConfigType  string    `orm:"size(20);default(string)" json:"config_type"` // string, number, boolean, json
	Description string    `orm:"type(text);null" json:"description"`
	IsEditable  bool      `orm:"default(true)" json:"is_editable"`
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt   time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`
}

// 表名
func (sc *SystemConfig) TableName() string {
	return "system_configs"
}

// 系统配置管理器
type SystemConfigManager struct{}

// 获取配置值（字符串）
func (scm *SystemConfigManager) GetString(key string, defaultValue string) string {
	o := orm.NewOrm()
	config := &SystemConfig{}
	err := o.QueryTable("system_configs").Filter("config_key", key).One(config)
	if err != nil {
		return defaultValue
	}
	return config.ConfigValue
}

// 获取配置值（整数）
func (scm *SystemConfigManager) GetInt(key string, defaultValue int) int {
	value := scm.GetString(key, "")
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}
	return intValue
}

// 获取配置值（浮点数）
func (scm *SystemConfigManager) GetFloat(key string, defaultValue float64) float64 {
	value := scm.GetString(key, "")
	if value == "" {
		return defaultValue
	}

	floatValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return defaultValue
	}
	return floatValue
}

// 获取配置值（布尔值）
func (scm *SystemConfigManager) GetBool(key string, defaultValue bool) bool {
	value := scm.GetString(key, "")
	if value == "" {
		return defaultValue
	}

	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return boolValue
}

// 获取配置值（JSON）
func (scm *SystemConfigManager) GetJSON(key string, target interface{}) error {
	value := scm.GetString(key, "")
	if value == "" {
		return orm.ErrNoRows
	}

	return json.Unmarshal([]byte(value), target)
}

// 设置配置值
func (scm *SystemConfigManager) SetConfig(key, value, configType, description string) error {
	o := orm.NewOrm()

	config := &SystemConfig{}
	err := o.QueryTable("system_configs").Filter("config_key", key).One(config)

	if err == orm.ErrNoRows {
		// 创建新配置
		config = &SystemConfig{
			ConfigKey:   key,
			ConfigValue: value,
			ConfigType:  configType,
			Description: description,
			IsEditable:  true,
		}
		_, err = o.Insert(config)
	} else if err == nil {
		// 更新现有配置
		if config.IsEditable {
			config.ConfigValue = value
			if configType != "" {
				config.ConfigType = configType
			}
			if description != "" {
				config.Description = description
			}
			_, err = o.Update(config)
		} else {
			return orm.ErrNoRows // 配置不可编辑
		}
	}

	return err
}

// 设置字符串配置
func (scm *SystemConfigManager) SetString(key, value, description string) error {
	return scm.SetConfig(key, value, "string", description)
}

// 设置整数配置
func (scm *SystemConfigManager) SetInt(key string, value int, description string) error {
	return scm.SetConfig(key, strconv.Itoa(value), "number", description)
}

// 设置浮点数配置
func (scm *SystemConfigManager) SetFloat(key string, value float64, description string) error {
	return scm.SetConfig(key, strconv.FormatFloat(value, 'f', -1, 64), "number", description)
}

// 设置布尔值配置
func (scm *SystemConfigManager) SetBool(key string, value bool, description string) error {
	return scm.SetConfig(key, strconv.FormatBool(value), "boolean", description)
}

// 设置JSON配置
func (scm *SystemConfigManager) SetJSON(key string, value interface{}, description string) error {
	jsonBytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return scm.SetConfig(key, string(jsonBytes), "json", description)
}

// 获取所有配置
func (scm *SystemConfigManager) GetAllConfigs() ([]*SystemConfig, error) {
	o := orm.NewOrm()
	var configs []*SystemConfig
	_, err := o.QueryTable("system_configs").OrderBy("config_key").All(&configs)
	return configs, err
}

// 获取可编辑的配置
func (scm *SystemConfigManager) GetEditableConfigs() ([]*SystemConfig, error) {
	o := orm.NewOrm()
	var configs []*SystemConfig
	_, err := o.QueryTable("system_configs").
		Filter("is_editable", true).
		OrderBy("config_key").
		All(&configs)
	return configs, err
}

// 删除配置
func (scm *SystemConfigManager) DeleteConfig(key string) error {
	o := orm.NewOrm()
	config := &SystemConfig{}
	err := o.QueryTable("system_configs").Filter("config_key", key).One(config)
	if err != nil {
		return err
	}

	if !config.IsEditable {
		return orm.ErrNoRows // 配置不可删除
	}

	_, err = o.Delete(config)
	return err
}

// 批量更新配置
func (scm *SystemConfigManager) BatchUpdateConfigs(configs map[string]string) error {
	o := orm.NewOrm()

	// 开始事务
	err := o.Begin()
	if err != nil {
		return err
	}

	for key, value := range configs {
		config := &SystemConfig{}
		err := o.QueryTable("system_configs").Filter("config_key", key).One(config)
		if err == nil && config.IsEditable {
			config.ConfigValue = value
			_, err = o.Update(config)
			if err != nil {
				o.Rollback()
				return err
			}
		}
	}

	return o.Commit()
}

// 重置配置为默认值
func (scm *SystemConfigManager) ResetToDefaults() error {
	o := orm.NewOrm()

	// 默认配置
	defaults := map[string]map[string]interface{}{
		"gender_check_enabled": {
			"value": "true",
			"type":  "boolean",
			"desc":  "是否启用性别校验",
		},
		"auto_merge_orders": {
			"value": "true",
			"type":  "boolean",
			"desc":  "是否自动合并同房间订单",
		},
		"max_beds_per_room": {
			"value": "8",
			"type":  "number",
			"desc":  "每个房间最大床位数",
		},
		"default_check_out_time": {
			"value": "12:00",
			"type":  "string",
			"desc":  "默认退房时间",
		},
		"allow_same_day_checkin": {
			"value": "true",
			"type":  "boolean",
			"desc":  "是否允许当日入住",
		},
		"room_status_refresh_interval": {
			"value": "30",
			"type":  "number",
			"desc":  "房间状态刷新间隔（秒）",
		},
	}

	// 开始事务
	err := o.Begin()
	if err != nil {
		return err
	}

	for key, configData := range defaults {
		err := scm.SetConfig(key, configData["value"].(string), configData["type"].(string), configData["desc"].(string))
		if err != nil {
			o.Rollback()
			return err
		}
	}

	return o.Commit()
}

// 系统配置服务
type SystemConfigService struct {
	manager *SystemConfigManager
}

// 创建系统配置服务
func NewSystemConfigService() *SystemConfigService {
	return &SystemConfigService{
		manager: &SystemConfigManager{},
	}
}

// 是否启用性别校验
func (scs *SystemConfigService) IsGenderCheckEnabled() bool {
	return scs.manager.GetBool("gender_check_enabled", true)
}

// 是否自动合并订单
func (scs *SystemConfigService) IsAutoMergeOrdersEnabled() bool {
	return scs.manager.GetBool("auto_merge_orders", true)
}

// 获取每个房间最大床位数
func (scs *SystemConfigService) GetMaxBedsPerRoom() int {
	return scs.manager.GetInt("max_beds_per_room", 8)
}

// 获取默认退房时间
func (scs *SystemConfigService) GetDefaultCheckOutTime() string {
	return scs.manager.GetString("default_check_out_time", "12:00")
}

// 是否允许当日入住
func (scs *SystemConfigService) IsSameDayCheckInAllowed() bool {
	return scs.manager.GetBool("allow_same_day_checkin", true)
}

// 获取房间状态刷新间隔
func (scs *SystemConfigService) GetRoomStatusRefreshInterval() int {
	return scs.manager.GetInt("room_status_refresh_interval", 30)
}
