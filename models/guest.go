package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 住客模型
type Guest struct {
	Id               int       `orm:"auto;pk" json:"id"`
	Name             string    `orm:"size(100)" json:"name"`
	Gender           string    `orm:"size(10)" json:"gender"` // male, female
	Phone            string    `orm:"size(20);null" json:"phone"`
	IdCard           string    `orm:"size(30);null" json:"id_card"`
	Email            string    `orm:"size(100);null" json:"email"`
	EmergencyContact string    `orm:"size(100);null" json:"emergency_contact"`
	EmergencyPhone   string    `orm:"size(20);null" json:"emergency_phone"`
	Notes            string    `orm:"type(text);null" json:"notes"`
	CreatedAt        time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt        time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`
}

// 表名
func (g *Guest) TableName() string {
	return "guests"
}

// 住客管理器
type GuestManager struct{}

// 创建住客
func (gm *GuestManager) CreateGuest(guest *Guest) error {
	o := orm.NewOrm()
	_, err := o.Insert(guest)
	return err
}

// 根据ID获取住客
func (gm *GuestManager) GetGuestById(id int) (*Guest, error) {
	o := orm.NewOrm()
	guest := &Guest{Id: id}
	err := o.Read(guest)
	return guest, err
}

// 根据手机号获取住客
func (gm *GuestManager) GetGuestByPhone(phone string) (*Guest, error) {
	o := orm.NewOrm()
	guest := &Guest{}
	err := o.QueryTable("guests").Filter("phone", phone).One(guest)
	return guest, err
}

// 根据身份证号获取住客
func (gm *GuestManager) GetGuestByIdCard(idCard string) (*Guest, error) {
	o := orm.NewOrm()
	guest := &Guest{}
	err := o.QueryTable("guests").Filter("id_card", idCard).One(guest)
	return guest, err
}

// 搜索住客
func (gm *GuestManager) SearchGuests(keyword string) ([]*Guest, error) {
	o := orm.NewOrm()
	var guests []*Guest
	_, err := o.QueryTable("guests").
		Filter("name__icontains", keyword).
		All(&guests)
	return guests, err
}

// 更新住客信息
func (gm *GuestManager) UpdateGuest(guest *Guest) error {
	o := orm.NewOrm()
	_, err := o.Update(guest)
	return err
}

// 获取所有住客
func (gm *GuestManager) GetAllGuests() ([]*Guest, error) {
	o := orm.NewOrm()
	var guests []*Guest
	_, err := o.QueryTable("guests").All(&guests)
	return guests, err
}

// 分页获取住客
func (gm *GuestManager) GetGuestsPaginated(page, pageSize int) ([]*Guest, int64, error) {
	o := orm.NewOrm()
	var guests []*Guest

	offset := (page - 1) * pageSize
	_, err := o.QueryTable("guests").
		OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&guests)

	total, _ := o.QueryTable("guests").Count()

	return guests, total, err
}

// 根据性别获取住客数量
func (gm *GuestManager) GetGuestCountByGender() (map[string]int, error) {
	o := orm.NewOrm()

	maleCount, _ := o.QueryTable("guests").Filter("gender", "male").Count()
	femaleCount, _ := o.QueryTable("guests").Filter("gender", "female").Count()

	result := map[string]int{
		"male":   int(maleCount),
		"female": int(femaleCount),
		"total":  int(maleCount + femaleCount),
	}

	return result, nil
}

// 检查住客是否存在
func (gm *GuestManager) GuestExists(phone, idCard string) (bool, *Guest, error) {
	o := orm.NewOrm()

	// 先检查手机号
	if phone != "" {
		guest := &Guest{}
		err := o.QueryTable("guests").Filter("phone", phone).One(guest)
		if err == nil {
			return true, guest, nil
		}
	}

	// 再检查身份证号
	if idCard != "" {
		guest := &Guest{}
		err := o.QueryTable("guests").Filter("id_card", idCard).One(guest)
		if err == nil {
			return true, guest, nil
		}
	}

	return false, nil, nil
}

// 住客详细信息（包含当前住宿状态）
type GuestDetail struct {
	*Guest
	CurrentRoom          *Room            `json:"current_room,omitempty"`
	CurrentOrder         *Order           `json:"current_order,omitempty"`
	AccommodationHistory []*Accommodation `json:"accommodation_history,omitempty"`
}

// 获取住客详细信息
func (gm *GuestManager) GetGuestDetail(guestId int) (*GuestDetail, error) {
	o := orm.NewOrm()

	// 获取住客基本信息
	guest, err := gm.GetGuestById(guestId)
	if err != nil {
		return nil, err
	}

	detail := &GuestDetail{Guest: guest}

	// 获取当前住宿信息
	accommodation := &Accommodation{}
	err = o.QueryTable("accommodations").
		Filter("guest_id", guestId).
		Filter("accommodation_status", "active").
		One(accommodation)

	if err == nil {
		// 获取房间信息
		room := &Room{Id: accommodation.RoomId}
		o.Read(room)
		detail.CurrentRoom = room

		// 获取订单信息
		order := &Order{Id: accommodation.OrderId}
		o.Read(order)
		detail.CurrentOrder = order
	}

	// 获取住宿历史
	var history []*Accommodation
	o.QueryTable("accommodations").
		Filter("guest_id", guestId).
		OrderBy("-created_at").
		All(&history)
	detail.AccommodationHistory = history

	return detail, nil
}

// 住客统计结构
type GuestStats struct {
	TotalGuests  int `json:"total_guests"`
	ActiveGuests int `json:"active_guests"`
	MaleGuests   int `json:"male_guests"`
	FemaleGuests int `json:"female_guests"`
}

// GetGuestStats 获取住客统计
func (gm *GuestManager) GetGuestStats() (*GuestStats, error) {
	o := orm.NewOrm()
	stats := &GuestStats{}

	// 总住客数
	total, _ := o.QueryTable("guests").Count()
	stats.TotalGuests = int(total)

	// 性别分布
	maleCount, _ := o.QueryTable("guests").Filter("gender", "male").Count()
	stats.MaleGuests = int(maleCount)

	femaleCount, _ := o.QueryTable("guests").Filter("gender", "female").Count()
	stats.FemaleGuests = int(femaleCount)

	// 活跃住客数（当前有住宿记录的）
	var activeCount int
	o.Raw("SELECT COUNT(DISTINCT guest_id) FROM accommodations WHERE accommodation_status = 'active'").QueryRow(&activeCount)
	stats.ActiveGuests = activeCount

	return stats, nil
}
