package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 订单模型
type Order struct {
	Id                 int       `orm:"auto;pk" json:"id"`
	OrderNumber        string    `orm:"size(50);unique" json:"order_number"`
	RoomId             int       `orm:"column(room_id)" json:"room_id"`
	GuestId            int       `orm:"column(guest_id)" json:"guest_id"`
	CheckInDate        time.Time `orm:"type(date)" json:"check_in_date"`
	CheckOutDate       time.Time `orm:"type(date);null" json:"check_out_date"`
	ActualCheckOutDate time.Time `orm:"type(date);null" json:"actual_check_out_date"`
	Nights             int       `orm:"default(1)" json:"nights"`
	BedCount           int       `orm:"default(1)" json:"bed_count"`
	TotalAmount        float64   `orm:"digits(10);decimals(2);default(0.00)" json:"total_amount"`
	PaidAmount         float64   `orm:"digits(10);decimals(2);default(0.00)" json:"paid_amount"`
	OrderStatus        string    `orm:"size(20);default(pending)" json:"order_status"`  // pending, checked_in, checked_out, cancelled
	PaymentStatus      string    `orm:"size(20);default(unpaid)" json:"payment_status"` // unpaid, partial, paid, refunded
	IsMerged           bool      `orm:"default(false)" json:"is_merged"`
	ParentOrderId      int       `orm:"null" json:"parent_order_id"`
	Notes              string    `orm:"type(text);null" json:"notes"`
	CreatedAt          time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt          time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`

	// 关联字段 - 手动加载，避免ORM自动字段冲突
	Room  *Room  `orm:"-" json:"room,omitempty"`
	Guest *Guest `orm:"-" json:"guest,omitempty"`
}

// 表名
func (o *Order) TableName() string {
	return "orders"
}

// 订单管理器
type OrderManager struct{}

// 创建订单
func (om *OrderManager) CreateOrder(order *Order) error {
	o := orm.NewOrm()
	_, err := o.Insert(order)
	return err
}

// 根据ID获取订单
func (om *OrderManager) GetOrderById(id int) (*Order, error) {
	o := orm.NewOrm()
	order := &Order{Id: id}
	err := o.Read(order)
	if err == nil {
		// 加载关联数据
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}
	return order, err
}

// 根据订单号获取订单
func (om *OrderManager) GetOrderByNumber(orderNumber string) (*Order, error) {
	o := orm.NewOrm()
	order := &Order{}
	err := o.QueryTable("orders").Filter("order_number", orderNumber).One(order)
	if err == nil {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}
	return order, err
}

// 获取房间的所有订单
func (om *OrderManager) GetOrdersByRoom(roomId int) ([]*Order, error) {
	o := orm.NewOrm()
	var orders []*Order
	_, err := o.QueryTable("orders").
		Filter("room_id", roomId).
		OrderBy("-created_at").
		All(&orders)

	// 加载关联数据
	for _, order := range orders {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}

	return orders, err
}

// 获取住客的所有订单
func (om *OrderManager) GetOrdersByGuest(guestId int) ([]*Order, error) {
	o := orm.NewOrm()
	var orders []*Order
	_, err := o.QueryTable("orders").
		Filter("guest_id", guestId).
		OrderBy("-created_at").
		All(&orders)

	for _, order := range orders {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}

	return orders, err
}

// 获取活跃订单（已入住但未退房）
func (om *OrderManager) GetActiveOrders() ([]*Order, error) {
	o := orm.NewOrm()
	var orders []*Order
	_, err := o.QueryTable("orders").
		Filter("order_status", "checked_in").
		OrderBy("-created_at").
		All(&orders)

	for _, order := range orders {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}

	return orders, err
}

// 更新订单状态
func (om *OrderManager) UpdateOrderStatus(orderId int, status string) error {
	o := orm.NewOrm()
	order := &Order{Id: orderId}
	if err := o.Read(order); err != nil {
		return err
	}

	order.OrderStatus = status
	if status == "checked_out" {
		order.ActualCheckOutDate = time.Now()
	}

	_, err := o.Update(order)
	return err
}

// 合并订单
func (om *OrderManager) MergeOrders(parentOrderId int, childOrderIds []int) error {
	o := orm.NewOrm()

	// 开始事务
	err := o.Begin()
	if err != nil {
		return err
	}

	// 更新子订单
	for _, childId := range childOrderIds {
		childOrder := &Order{Id: childId}
		if err := o.Read(childOrder); err != nil {
			o.Rollback()
			return err
		}

		childOrder.ParentOrderId = parentOrderId
		childOrder.IsMerged = true

		if _, err := o.Update(childOrder); err != nil {
			o.Rollback()
			return err
		}
	}

	// 更新父订单
	parentOrder := &Order{Id: parentOrderId}
	if err := o.Read(parentOrder); err != nil {
		o.Rollback()
		return err
	}

	parentOrder.IsMerged = true
	if _, err := o.Update(parentOrder); err != nil {
		o.Rollback()
		return err
	}

	return o.Commit()
}

// GetRevenueStats 获取收入统计
func (om *OrderManager) GetRevenueStats(period string) ([]map[string]interface{}, error) {
	o := orm.NewOrm()
	var result []map[string]interface{}

	var groupBy string

	switch period {
	case "day":
		groupBy = "DATE(created_at)"
	case "week":
		groupBy = "YEARWEEK(created_at)"
	case "month":
		groupBy = "DATE_FORMAT(created_at, '%Y-%m')"
	case "year":
		groupBy = "YEAR(created_at)"
	default:
		groupBy = "DATE_FORMAT(created_at, '%Y-%m')"
	}

	// 查询收入数据
	var revenueData []orm.Params
	_, err := o.Raw(`SELECT ` + groupBy + ` as period, 
					SUM(total_amount) as total_revenue,
					SUM(paid_amount) as paid_revenue,
					COUNT(*) as order_count
					FROM orders 
					WHERE order_status != 'cancelled'
					GROUP BY ` + groupBy + `
					ORDER BY period DESC
					LIMIT 30`).Values(&revenueData)

	if err != nil {
		return result, err
	}

	for _, data := range revenueData {
		result = append(result, map[string]interface{}{
			"period":        data["period"],
			"total_revenue": data["total_revenue"],
			"paid_revenue":  data["paid_revenue"],
			"order_count":   data["order_count"],
		})
	}

	return result, nil
}

// 生成订单号
func (om *OrderManager) GenerateOrderNumber() string {
	return time.Now().Format("20060102150405") + "001"
}

// 订单统计
type OrderStats struct {
	TotalOrders     int     `json:"total_orders"`
	ActiveOrders    int     `json:"active_orders"`
	CompletedOrders int     `json:"completed_orders"`
	CancelledOrders int     `json:"cancelled_orders"`
	TotalRevenue    float64 `json:"total_revenue"`
	PendingRevenue  float64 `json:"pending_revenue"`
}

// 获取订单统计
func (om *OrderManager) GetOrderStats() (*OrderStats, error) {
	o := orm.NewOrm()
	stats := &OrderStats{}

	// 总订单数
	total, _ := o.QueryTable("orders").Count()
	stats.TotalOrders = int(total)

	// 各状态订单数
	active, _ := o.QueryTable("orders").Filter("order_status", "checked_in").Count()
	stats.ActiveOrders = int(active)

	completed, _ := o.QueryTable("orders").Filter("order_status", "checked_out").Count()
	stats.CompletedOrders = int(completed)

	cancelled, _ := o.QueryTable("orders").Filter("order_status", "cancelled").Count()
	stats.CancelledOrders = int(cancelled)

	// 收入统计
	var orders []*Order
	o.QueryTable("orders").Filter("order_status__in", "checked_out", "checked_in").All(&orders)

	for _, order := range orders {
		if order.OrderStatus == "checked_out" {
			stats.TotalRevenue += order.PaidAmount
		} else {
			stats.PendingRevenue += order.TotalAmount - order.PaidAmount
		}
	}

	return stats, nil
}

// 分页获取订单
func (om *OrderManager) GetOrdersPaginated(page, pageSize int, status string) ([]*Order, int64, error) {
	o := orm.NewOrm()
	var orders []*Order

	qs := o.QueryTable("orders")
	if status != "" {
		qs = qs.Filter("order_status", status)
	}

	offset := (page - 1) * pageSize
	_, err := qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&orders)

	// 加载关联数据
	for _, order := range orders {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}

	total, _ := qs.Count()

	return orders, total, err
}

// GetOrdersByStatus 根据状态获取订单
func (om *OrderManager) GetOrdersByStatus(status string) ([]*Order, error) {
	o := orm.NewOrm()
	var orders []*Order
	_, err := o.QueryTable("orders").Filter("order_status", status).All(&orders)
	return orders, err
}

// UpdateOrder 更新订单信息
func (om *OrderManager) UpdateOrder(order *Order) error {
	o := orm.NewOrm()
	_, err := o.Update(order)
	return err
}

// GetAllOrders 获取所有订单
func (om *OrderManager) GetAllOrders() ([]*Order, error) {
	o := orm.NewOrm()
	var orders []*Order
	_, err := o.QueryTable("orders").OrderBy("-created_at").All(&orders)

	// 加载关联数据
	for _, order := range orders {
		o.LoadRelated(order, "Room")
		o.LoadRelated(order, "Guest")
	}

	return orders, err
}

// SplitOrder 拆分订单
func (om *OrderManager) SplitOrder(orderId int, splitData map[string]interface{}) error {
	o := orm.NewOrm()

	// 开始事务
	err := o.Begin()
	if err != nil {
		return err
	}

	// 获取原订单
	originalOrder := &Order{Id: orderId}
	if err := o.Read(originalOrder); err != nil {
		o.Rollback()
		return err
	}

	// 创建新订单
	newOrder := &Order{
		OrderNumber:   om.GenerateOrderNumber(),
		RoomId:        originalOrder.RoomId,
		GuestId:       originalOrder.GuestId,
		CheckInDate:   originalOrder.CheckInDate,
		CheckOutDate:  originalOrder.CheckOutDate,
		Nights:        originalOrder.Nights,
		BedCount:      1,                             // 拆分后每个订单默认1个床位
		TotalAmount:   originalOrder.TotalAmount / 2, // 简单平分金额
		PaidAmount:    0,
		OrderStatus:   "pending",
		PaymentStatus: "unpaid",
		IsMerged:      false,
		Notes:         "从订单 " + originalOrder.OrderNumber + " 拆分",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 插入新订单
	if _, err := o.Insert(newOrder); err != nil {
		o.Rollback()
		return err
	}

	// 更新原订单
	originalOrder.BedCount = originalOrder.BedCount - 1
	originalOrder.TotalAmount = originalOrder.TotalAmount / 2
	originalOrder.Notes = "已拆分出订单 " + newOrder.OrderNumber
	originalOrder.UpdatedAt = time.Now()

	if _, err := o.Update(originalOrder); err != nil {
		o.Rollback()
		return err
	}

	return o.Commit()
}
