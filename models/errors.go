package models

import "fmt"

// 自定义错误类型
type BedSharingError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *BedSharingError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 创建新错误
func NewError(code, message string) *BedSharingError {
	return &BedSharingError{
		Code:    code,
		Message: message,
	}
}

// 常用错误定义
var (
	ErrRoomNotFound     = NewError("ROOM_NOT_FOUND", "房间不存在")
	ErrRoomFull         = NewError("ROOM_FULL", "房间已满")
	ErrGuestNotFound    = NewError("GUEST_NOT_FOUND", "住客不存在")
	ErrOrderNotFound    = NewError("ORDER_NOT_FOUND", "订单不存在")
	ErrGenderConflict   = NewError("GENDER_CONFLICT", "性别冲突")
	ErrBedNotAvailable  = NewError("BED_NOT_AVAILABLE", "床位不可用")
	ErrInvalidParameter = NewError("INVALID_PARAMETER", "参数无效")
	ErrDatabaseError    = NewError("DATABASE_ERROR", "数据库错误")
	ErrPermissionDenied = NewError("PERMISSION_DENIED", "权限不足")
	ErrOperationFailed  = NewError("OPERATION_FAILED", "操作失败")
)

// 检查错误类型
func IsError(err error, targetErr *BedSharingError) bool {
	if bedErr, ok := err.(*BedSharingError); ok {
		return bedErr.Code == targetErr.Code
	}
	return false
}

// 包装标准错误
func WrapError(code, message string, err error) *BedSharingError {
	if err != nil {
		message = fmt.Sprintf("%s: %v", message, err)
	}
	return NewError(code, message)
}
