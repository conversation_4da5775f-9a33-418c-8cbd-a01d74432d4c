package models

import (
	"time"

	"github.com/astaxie/beego/orm"
)

// 房间模型
type Room struct {
	Id            int       `orm:"auto;pk" json:"id"`
	RoomNumber    string    `orm:"size(20);unique" json:"room_number"`
	TotalBeds     int       `orm:"default(0)" json:"total_beds"`
	OccupiedBeds  int       `orm:"default(0)" json:"occupied_beds"`
	RoomType      string    `orm:"size(20);default(mixed)" json:"room_type"`     // mixed, male_only, female_only
	CurrentGender string    `orm:"size(10);default(none)" json:"current_gender"` // none, male, female
	FloorNumber   int       `json:"floor_number"`
	RoomStatus    string    `orm:"size(20);default(available)" json:"room_status"` // available, occupied, maintenance, full
	PricePerBed   float64   `orm:"digits(10);decimals(2);default(0.00)" json:"price_per_bed"`
	Description   string    `orm:"type(text)" json:"description"`
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
	UpdatedAt     time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`
}

// 表名
func (r *Room) TableName() string {
	return "rooms"
}

// 获取可用床位数
func (r *Room) GetAvailableBeds() int {
	return r.TotalBeds - r.OccupiedBeds
}

// 检查房间是否可用
func (r *Room) IsAvailable() bool {
	return r.RoomStatus == "available" && r.GetAvailableBeds() > 0
}

// 检查性别兼容性
func (r *Room) CheckGenderCompatibility(guestGender string) bool {
	if r.CurrentGender == "none" || r.CurrentGender == guestGender {
		return true
	}
	return false
}

// 房间管理器
type RoomManager struct{}

// 获取所有房间
func (rm *RoomManager) GetAllRooms() ([]*Room, error) {
	o := orm.NewOrm()
	var rooms []*Room
	_, err := o.QueryTable("rooms").All(&rooms)
	return rooms, err
}

// 根据状态获取房间
func (rm *RoomManager) GetRoomsByStatus(status string) ([]*Room, error) {
	o := orm.NewOrm()
	var rooms []*Room
	_, err := o.QueryTable("rooms").Filter("room_status", status).All(&rooms)
	return rooms, err
}

// 获取所有可用房间
func (rm *RoomManager) GetAvailableRooms() ([]*Room, error) {
	o := orm.NewOrm()
	var rooms []*Room
	_, err := o.QueryTable("rooms").
		Filter("room_status", "available").
		All(&rooms)

	// 过滤出有空床位的房间
	var availableRooms []*Room
	for _, room := range rooms {
		if room.OccupiedBeds < room.TotalBeds {
			availableRooms = append(availableRooms, room)
		}
	}

	return availableRooms, err
}

// 根据性别获取可用房间
func (rm *RoomManager) GetAvailableRoomsByGender(gender string) ([]*Room, error) {
	o := orm.NewOrm()
	var rooms []*Room
	_, err := o.QueryTable("rooms").
		Filter("room_status", "available").
		Filter("current_gender__in", "none", gender).
		All(&rooms)
	return rooms, err
}

// 获取房间详情
func (rm *RoomManager) GetRoomById(id int) (*Room, error) {
	o := orm.NewOrm()
	room := &Room{Id: id}
	err := o.Read(room)
	return room, err
}

// 更新房间状态
func (rm *RoomManager) UpdateRoomStatus(roomId int, occupiedBeds int, currentGender string) error {
	o := orm.NewOrm()
	room := &Room{Id: roomId}
	if err := o.Read(room); err != nil {
		return err
	}

	room.OccupiedBeds = occupiedBeds
	room.CurrentGender = currentGender

	// 根据占用情况更新房间状态
	if occupiedBeds >= room.TotalBeds {
		room.RoomStatus = "full"
	} else if occupiedBeds > 0 {
		room.RoomStatus = "occupied"
	} else {
		room.RoomStatus = "available"
		room.CurrentGender = "none"
	}

	_, err := o.Update(room)
	return err
}

// 房间统计信息
type RoomStats struct {
	TotalRooms     int `json:"total_rooms"`
	AvailableRooms int `json:"available_rooms"`
	OccupiedRooms  int `json:"occupied_rooms"`
	FullRooms      int `json:"full_rooms"`
	MaleRooms      int `json:"male_rooms"`
	FemaleRooms    int `json:"female_rooms"`
	TotalBeds      int `json:"total_beds"`
	OccupiedBeds   int `json:"occupied_beds"`
	AvailableBeds  int `json:"available_beds"`
}

// 获取房间统计
func (rm *RoomManager) GetRoomStats() (*RoomStats, error) {
	o := orm.NewOrm()
	stats := &RoomStats{}

	// 总房间数
	total, _ := o.QueryTable("rooms").Count()
	stats.TotalRooms = int(total)

	// 各状态房间数
	available, _ := o.QueryTable("rooms").Filter("room_status", "available").Count()
	stats.AvailableRooms = int(available)

	occupied, _ := o.QueryTable("rooms").Filter("room_status", "occupied").Count()
	stats.OccupiedRooms = int(occupied)

	full, _ := o.QueryTable("rooms").Filter("room_status", "full").Count()
	stats.FullRooms = int(full)

	// 性别分布
	male, _ := o.QueryTable("rooms").Filter("current_gender", "male").Count()
	stats.MaleRooms = int(male)

	female, _ := o.QueryTable("rooms").Filter("current_gender", "female").Count()
	stats.FemaleRooms = int(female)

	// 床位统计
	var rooms []*Room
	o.QueryTable("rooms").All(&rooms)
	for _, room := range rooms {
		stats.TotalBeds += room.TotalBeds
		stats.OccupiedBeds += room.OccupiedBeds
	}

	// 计算可用床位数
	stats.AvailableBeds = stats.TotalBeds - stats.OccupiedBeds

	return stats, nil
}

// GetRoomTypeStats 获取房间类型统计
func (rm *RoomManager) GetRoomTypeStats() ([]map[string]interface{}, error) {
	o := orm.NewOrm()
	var result []map[string]interface{}

	// 查询房间类型统计
	var roomTypeData []orm.Params
	_, err := o.Raw(`SELECT room_type, 
					COUNT(*) as room_count,
					SUM(total_beds) as total_beds,
					SUM(CASE WHEN room_status = 'occupied' THEN 1 ELSE 0 END) as occupied_rooms,
					AVG(total_beds) as avg_beds_per_room
					FROM rooms 
					GROUP BY room_type
					ORDER BY room_count DESC`).Values(&roomTypeData)

	if err != nil {
		return result, err
	}

	for _, data := range roomTypeData {
		result = append(result, map[string]interface{}{
			"room_type":         data["room_type"],
			"room_count":        data["room_count"],
			"total_beds":        data["total_beds"],
			"occupied_rooms":    data["occupied_rooms"],
			"avg_beds_per_room": data["avg_beds_per_room"],
		})
	}

	return result, nil
}
