package models

import (
	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	_ "github.com/go-sql-driver/mysql"
)

// 初始化数据库
func InitDB() {
	// 注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)

	// 获取数据库配置
	dbHost := beego.AppConfig.String("mysqlurls")
	dbPort := beego.AppConfig.String("mysqlport")
	dbUser := beego.AppConfig.String("mysqluser")
	dbPassword := beego.AppConfig.String("mysqlpass")
	dbName := beego.AppConfig.String("mysqldb")

	// 构建数据库连接字符串
	dataSource := dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=true&loc=Local"

	// 注册数据库连接
	err := orm.RegisterDataBase("default", "mysql", dataSource)
	if err != nil {
		beego.Error("注册数据库失败:", err)
		panic(err)
	}

	// 注册模型
	registerModels()

	// 开发模式下自动建表
	if beego.BConfig.RunMode == "dev" {
		err = orm.RunSyncdb("default", false, true)
		if err != nil {
			beego.Error("同步数据库表失败:", err)
		}
	}

	beego.Info("数据库初始化完成")
}

// 注册所有模型
func registerModels() {
	orm.RegisterModel(
		new(Room),
		new(Guest),
		new(Order),
		new(Accommodation),
		new(GenderRule),
		new(OperationLog),
		new(SystemConfig),
	)
}

// 获取ORM实例
func GetOrm() orm.Ormer {
	return orm.NewOrm()
}

// 数据库健康检查
func CheckDBHealth() error {
	o := orm.NewOrm()
	_, err := o.Raw("SELECT 1").Exec()
	return err
}
