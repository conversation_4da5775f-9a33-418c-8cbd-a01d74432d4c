-- 拼床模式住宿管理系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS bed_sharing_system DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE bed_sharing_system;

-- 1. 房间表 (rooms)
CREATE TABLE rooms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_number VARCHAR(20) NOT NULL UNIQUE COMMENT '房间号',
    total_beds INT NOT NULL DEFAULT 0 COMMENT '总床位数',
    occupied_beds INT NOT NULL DEFAULT 0 COMMENT '已占用床位数',
    room_type ENUM('mixed', 'male_only', 'female_only') NOT NULL DEFAULT 'mixed' COMMENT '房间类型：混合/仅男性/仅女性',
    current_gender ENUM('none', 'male', 'female') DEFAULT 'none' COMMENT '当前房间性别（用于性别隔离）',
    floor_number INT COMMENT '楼层号',
    room_status ENUM('available', 'occupied', 'maintenance', 'full') NOT NULL DEFAULT 'available' COMMENT '房间状态',
    price_per_bed DECIMAL(10,2) DEFAULT 0.00 COMMENT '每床位价格',
    description TEXT COMMENT '房间描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_room_number (room_number),
    INDEX idx_room_status (room_status),
    INDEX idx_current_gender (current_gender),
    INDEX idx_floor (floor_number)
) ENGINE=InnoDB COMMENT='房间信息表';

-- 2. 住客表 (guests)
CREATE TABLE guests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '住客姓名',
    gender ENUM('male', 'female') NOT NULL COMMENT '性别',
    phone VARCHAR(20) COMMENT '联系电话',
    id_card VARCHAR(30) COMMENT '身份证号',
    email VARCHAR(100) COMMENT '邮箱',
    emergency_contact VARCHAR(100) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_gender (gender)
) ENGINE=InnoDB COMMENT='住客信息表';

-- 3. 订单表 (orders)
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单编号',
    room_id INT NOT NULL COMMENT '房间ID',
    guest_id INT NOT NULL COMMENT '主要住客ID',
    check_in_date DATE NOT NULL COMMENT '入住日期',
    check_out_date DATE COMMENT '预计退房日期',
    actual_check_out_date DATE COMMENT '实际退房日期',
    nights INT NOT NULL DEFAULT 1 COMMENT '住宿天数',
    bed_count INT NOT NULL DEFAULT 1 COMMENT '床位数量',
    total_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '总金额',
    paid_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '已付金额',
    order_status ENUM('pending', 'checked_in', 'checked_out', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
    payment_status ENUM('unpaid', 'partial', 'paid', 'refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
    is_merged BOOLEAN DEFAULT FALSE COMMENT '是否为合并订单',
    parent_order_id INT COMMENT '父订单ID（用于订单合并）',
    notes TEXT COMMENT '订单备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE RESTRICT,
    FOREIGN KEY (guest_id) REFERENCES guests(id) ON DELETE RESTRICT,
    FOREIGN KEY (parent_order_id) REFERENCES orders(id) ON DELETE SET NULL,
    
    INDEX idx_order_number (order_number),
    INDEX idx_room_id (room_id),
    INDEX idx_guest_id (guest_id),
    INDEX idx_check_in_date (check_in_date),
    INDEX idx_order_status (order_status),
    INDEX idx_parent_order (parent_order_id)
) ENGINE=InnoDB COMMENT='订单信息表';

-- 4. 住宿记录表 (accommodations)
CREATE TABLE accommodations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '订单ID',
    room_id INT NOT NULL COMMENT '房间ID',
    guest_id INT NOT NULL COMMENT '住客ID',
    bed_number VARCHAR(10) COMMENT '床位号',
    check_in_time TIMESTAMP COMMENT '实际入住时间',
    check_out_time TIMESTAMP COMMENT '实际退房时间',
    accommodation_status ENUM('active', 'completed', 'cancelled') NOT NULL DEFAULT 'active' COMMENT '住宿状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE RESTRICT,
    FOREIGN KEY (guest_id) REFERENCES guests(id) ON DELETE RESTRICT,
    
    INDEX idx_order_id (order_id),
    INDEX idx_room_id (room_id),
    INDEX idx_guest_id (guest_id),
    INDEX idx_status (accommodation_status),
    
    UNIQUE KEY uk_room_bed (room_id, bed_number, accommodation_status)
) ENGINE=InnoDB COMMENT='住宿记录表';

-- 5. 性别校验规则表 (gender_rules)
CREATE TABLE gender_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type ENUM('strict', 'flexible', 'disabled') NOT NULL DEFAULT 'strict' COMMENT '规则类型',
    description TEXT COMMENT '规则描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='性别校验规则表';

-- 6. 操作日志表 (operation_logs)
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    operator VARCHAR(100) COMMENT '操作员',
    operation_type ENUM('check_in', 'check_out', 'room_assign', 'order_merge', 'gender_check') NOT NULL COMMENT '操作类型',
    target_type ENUM('room', 'guest', 'order', 'accommodation') NOT NULL COMMENT '目标类型',
    target_id INT NOT NULL COMMENT '目标ID',
    operation_detail TEXT COMMENT '操作详情',
    result ENUM('success', 'failed', 'warning') NOT NULL COMMENT '操作结果',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_operator (operator),
    INDEX idx_operation_type (operation_type),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='操作日志表';

-- 7. 系统配置表 (system_configs)
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入默认数据

-- 插入性别校验规则
INSERT INTO gender_rules (rule_name, rule_type, description, is_active) VALUES
('严格性别隔离', 'strict', '同一房间只能分配相同性别的住客，系统自动拦截违规操作', TRUE),
('灵活性别管理', 'flexible', '允许管理员手动覆盖性别限制，但会记录警告日志', FALSE),
('禁用性别检查', 'disabled', '完全禁用性别校验功能', FALSE);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_editable) VALUES
('gender_check_enabled', 'true', 'boolean', '是否启用性别校验', TRUE),
('auto_merge_orders', 'true', 'boolean', '是否自动合并同房间订单', TRUE),
('max_beds_per_room', '8', 'number', '每个房间最大床位数', TRUE),
('default_check_out_time', '12:00', 'string', '默认退房时间', TRUE),
('allow_same_day_checkin', 'true', 'boolean', '是否允许当日入住', TRUE),
('room_status_refresh_interval', '30', 'number', '房间状态刷新间隔（秒）', TRUE);

-- 插入示例房间数据
INSERT INTO rooms (room_number, total_beds, room_type, floor_number, price_per_bed, description) VALUES
('101', 4, 'mixed', 1, 50.00, '一楼四人间，配备空调和独立卫浴'),
('102', 6, 'mixed', 1, 45.00, '一楼六人间，经济实惠'),
('201', 4, 'mixed', 2, 55.00, '二楼四人间，采光良好'),
('202', 8, 'mixed', 2, 40.00, '二楼八人间，适合团体入住'),
('301', 2, 'mixed', 3, 80.00, '三楼双人间，安静舒适'),
('302', 4, 'mixed', 3, 60.00, '三楼四人间，高级配置');

-- 创建视图：房间状态概览
CREATE VIEW room_status_overview AS
SELECT 
    r.id,
    r.room_number,
    r.total_beds,
    r.occupied_beds,
    (r.total_beds - r.occupied_beds) AS available_beds,
    r.current_gender,
    r.room_status,
    r.price_per_bed,
    COUNT(a.id) AS current_guests,
    GROUP_CONCAT(g.name SEPARATOR ', ') AS guest_names
FROM rooms r
LEFT JOIN accommodations a ON r.id = a.room_id AND a.accommodation_status = 'active'
LEFT JOIN guests g ON a.guest_id = g.id
GROUP BY r.id;

-- 创建存储过程：性别校验
DELIMITER //
CREATE PROCEDURE CheckGenderCompatibility(
    IN p_room_id INT,
    IN p_guest_gender ENUM('male', 'female'),
    OUT p_result BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_current_gender ENUM('none', 'male', 'female');
    DECLARE v_occupied_beds INT;
    DECLARE v_total_beds INT;
    
    -- 获取房间信息
    SELECT current_gender, occupied_beds, total_beds 
    INTO v_current_gender, v_occupied_beds, v_total_beds
    FROM rooms WHERE id = p_room_id;
    
    -- 检查房间是否已满
    IF v_occupied_beds >= v_total_beds THEN
        SET p_result = FALSE;
        SET p_message = '房间已满，无法分配';
    -- 检查性别兼容性
    ELSEIF v_current_gender = 'none' OR v_current_gender = p_guest_gender THEN
        SET p_result = TRUE;
        SET p_message = '性别校验通过';
    ELSE
        SET p_result = FALSE;
        SET p_message = CONCAT('性别冲突：房间当前为', 
            CASE v_current_gender 
                WHEN 'male' THEN '男性' 
                WHEN 'female' THEN '女性' 
            END, 
            '专用，无法分配', 
            CASE p_guest_gender 
                WHEN 'male' THEN '男性' 
                WHEN 'female' THEN '女性' 
            END, 
            '住客');
    END IF;
END //
DELIMITER ;

-- 创建触发器：更新房间状态
DELIMITER //
CREATE TRIGGER update_room_status_after_accommodation
AFTER INSERT ON accommodations
FOR EACH ROW
BEGIN
    DECLARE v_occupied_count INT;
    DECLARE v_total_beds INT;
    DECLARE v_guest_gender ENUM('male', 'female');
    
    -- 获取住客性别
    SELECT gender INTO v_guest_gender FROM guests WHERE id = NEW.guest_id;
    
    -- 计算当前占用床位数
    SELECT COUNT(*) INTO v_occupied_count 
    FROM accommodations 
    WHERE room_id = NEW.room_id AND accommodation_status = 'active';
    
    -- 获取房间总床位数
    SELECT total_beds INTO v_total_beds FROM rooms WHERE id = NEW.room_id;
    
    -- 更新房间信息
    UPDATE rooms SET 
        occupied_beds = v_occupied_count,
        current_gender = CASE 
            WHEN v_occupied_count = 0 THEN 'none'
            ELSE v_guest_gender
        END,
        room_status = CASE 
            WHEN v_occupied_count >= v_total_beds THEN 'full'
            WHEN v_occupied_count > 0 THEN 'occupied'
            ELSE 'available'
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.room_id;
END //
DELIMITER ;

-- 创建触发器：退房后更新房间状态
DELIMITER //
CREATE TRIGGER update_room_status_after_checkout
AFTER UPDATE ON accommodations
FOR EACH ROW
BEGIN
    DECLARE v_occupied_count INT;
    DECLARE v_total_beds INT;
    DECLARE v_remaining_gender ENUM('male', 'female');
    
    -- 只在状态变为completed时触发
    IF OLD.accommodation_status = 'active' AND NEW.accommodation_status = 'completed' THEN
        -- 计算当前占用床位数
        SELECT COUNT(*) INTO v_occupied_count 
        FROM accommodations 
        WHERE room_id = NEW.room_id AND accommodation_status = 'active';
        
        -- 获取房间总床位数
        SELECT total_beds INTO v_total_beds FROM rooms WHERE id = NEW.room_id;
        
        -- 获取剩余住客的性别（如果有的话）
        SELECT g.gender INTO v_remaining_gender
        FROM accommodations a
        JOIN guests g ON a.guest_id = g.id
        WHERE a.room_id = NEW.room_id AND a.accommodation_status = 'active'
        LIMIT 1;
        
        -- 更新房间信息
        UPDATE rooms SET 
            occupied_beds = v_occupied_count,
            current_gender = CASE 
                WHEN v_occupied_count = 0 THEN 'none'
                ELSE IFNULL(v_remaining_gender, 'none')
            END,
            room_status = CASE 
                WHEN v_occupied_count >= v_total_beds THEN 'full'
                WHEN v_occupied_count > 0 THEN 'occupied'
                ELSE 'available'
            END,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.room_id;
    END IF;
END //
DELIMITER ;