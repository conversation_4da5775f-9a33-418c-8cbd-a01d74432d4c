<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键分配测试 - 拼床住宿管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/@tdesign/web-vue@1.6.7/dist/tdesign.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e7e7e7;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: #0052d9;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #003ba3;
        }
        .btn-secondary {
            background: #f3f3f3;
            color: #666;
        }
        .btn-secondary:hover {
            background: #e6e6e6;
        }
        .result {
            margin-top: 20px;
            padding: 16px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .result.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .result.warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
        }
        .guest-list {
            margin-top: 20px;
        }
        .guest-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e7e7e7;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #fafafa;
        }
        .guest-item input {
            margin-right: 10px;
            width: auto;
        }
        .add-guest {
            background: #52c41a;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>一键分配功能测试</h1>
            <p>测试智能房间分配和性别冲突检测功能</p>
        </div>

        <form id="assignmentForm">
            <div class="form-group">
                <label>房间类型偏好：</label>
                <select name="roomType" id="roomType">
                    <option value="">不限</option>
                    <option value="standard">标准间</option>
                    <option value="deluxe">豪华间</option>
                    <option value="suite">套房</option>
                </select>
            </div>

            <div class="form-group">
                <label>入住日期：</label>
                <input type="date" name="checkInDate" id="checkInDate" required>
            </div>

            <div class="form-group">
                <label>退房日期：</label>
                <input type="date" name="checkOutDate" id="checkOutDate" required>
            </div>

            <div class="guest-list">
                <label>住客信息：</label>
                <div id="guestContainer">
                    <div class="guest-item">
                        <input type="text" placeholder="姓名" name="guestName[]" required>
                        <select name="guestGender[]" required>
                            <option value="">选择性别</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                        </select>
                        <input type="text" placeholder="身份证号" name="guestIdCard[]" required>
                        <input type="text" placeholder="联系电话" name="guestPhone[]" required>
                    </div>
                </div>
                <button type="button" class="add-guest" onclick="addGuest()">+ 添加住客</button>
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <button type="submit" class="btn">智能分配房间</button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置表单</button>
            </div>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        let guestCount = 1;

        function addGuest() {
            guestCount++;
            const container = document.getElementById('guestContainer');
            const guestItem = document.createElement('div');
            guestItem.className = 'guest-item';
            guestItem.innerHTML = `
                <input type="text" placeholder="姓名" name="guestName[]" required>
                <select name="guestGender[]" required>
                    <option value="">选择性别</option>
                    <option value="male">男</option>
                    <option value="female">女</option>
                </select>
                <input type="text" placeholder="身份证号" name="guestIdCard[]" required>
                <input type="text" placeholder="联系电话" name="guestPhone[]" required>
                <button type="button" onclick="removeGuest(this)" style="background: #ff4d4f; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">删除</button>
            `;
            container.appendChild(guestItem);
        }

        function removeGuest(button) {
            if (guestCount > 1) {
                button.parentElement.remove();
                guestCount--;
            }
        }

        function resetForm() {
            document.getElementById('assignmentForm').reset();
            const container = document.getElementById('guestContainer');
            container.innerHTML = `
                <div class="guest-item">
                    <input type="text" placeholder="姓名" name="guestName[]" required>
                    <select name="guestGender[]" required>
                        <option value="">选择性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                    <input type="text" placeholder="身份证号" name="guestIdCard[]" required>
                    <input type="text" placeholder="联系电话" name="guestPhone[]" required>
                </div>
            `;
            guestCount = 1;
            document.getElementById('result').style.display = 'none';
        }

        document.getElementById('assignmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const guests = [];
            
            // 收集住客信息
            const names = formData.getAll('guestName[]');
            const genders = formData.getAll('guestGender[]');
            const idCards = formData.getAll('guestIdCard[]');
            const phones = formData.getAll('guestPhone[]');
            
            for (let i = 0; i < names.length; i++) {
                if (names[i] && genders[i] && idCards[i] && phones[i]) {
                    guests.push({
                        name: names[i],
                        gender: genders[i],
                        id_card: idCards[i],
                        phone: phones[i]
                    });
                }
            }
            
            // 检查性别一致性
            const genderSet = new Set(guests.map(g => g.gender));
            if (genderSet.size > 1) {
                showResult('error', '性别冲突警告：所有住客必须为同一性别！系统已自动拦截此次分配操作。');
                return;
            }
            
            const requestData = {
                room_type: formData.get('roomType'),
                check_in_date: formData.get('checkInDate'),
                check_out_date: formData.get('checkOutDate'),
                guests: guests
            };
            
            // 发送分配请求
            fetch('/assignment/smart-assign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('success', `分配成功！${data.message}`);
                } else {
                    if (data.message && data.message.includes('性别冲突')) {
                        showResult('warning', `性别冲突警告：${data.message}`);
                    } else {
                        showResult('error', `分配失败：${data.message || '未知错误'}`);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResult('error', '网络请求失败，请检查服务器连接。');
            });
        });

        function showResult(type, message) {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
            result.style.display = 'block';
            
            // 滚动到结果区域
            result.scrollIntoView({ behavior: 'smooth' });
        }

        // 设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            document.getElementById('checkInDate').value = today.toISOString().split('T')[0];
            document.getElementById('checkOutDate').value = tomorrow.toISOString().split('T')[0];
        });
    </script>
</body>
</html>