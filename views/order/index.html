<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 拼床住宿管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/@tdesign/web-vue@1.6.7/dist/tdesign.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #0052d9 0%, #003ba3 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .toolbar {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }
        .search-filters {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-filters input,
        .search-filters select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .btn-primary {
            background: #0052d9;
            color: white;
        }
        .btn-primary:hover {
            background: #003ba3;
        }
        .btn-success {
            background: #52c41a;
            color: white;
        }
        .btn-success:hover {
            background: #389e0d;
        }
        .btn-warning {
            background: #fa8c16;
            color: white;
        }
        .btn-warning:hover {
            background: #d46b08;
        }
        .btn-secondary {
            background: #f3f3f3;
            color: #666;
        }
        .btn-secondary:hover {
            background: #e6e6e6;
        }
        .orders-grid {
            display: grid;
            gap: 16px;
        }
        .order-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .order-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e7e7e7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .order-number {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .order-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending { background: #fff7e6; color: #d48806; }
        .status-confirmed { background: #f6ffed; color: #389e0d; }
        .status-checked_in { background: #e6f7ff; color: #1890ff; }
        .status-completed { background: #f9f0ff; color: #722ed1; }
        .status-cancelled { background: #fff2f0; color: #cf1322; }
        .order-content {
            padding: 20px;
        }
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .info-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        .info-content h4 {
            margin: 0 0 2px 0;
            font-size: 12px;
            color: #666;
        }
        .info-content p {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        .guests-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }
        .guests-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        .guests-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .guest-tag {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 16px;
            font-size: 12px;
            color: #666;
        }
        .guest-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #0052d9;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }
        .order-actions {
            padding: 16px 20px;
            background: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        .merge-section {
            background: #fff7e6;
            border: 1px solid #ffe58f;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .merge-title {
            font-size: 16px;
            font-weight: 600;
            color: #d48806;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .merge-controls {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state img {
            width: 120px;
            height: 120px;
            opacity: 0.5;
            margin-bottom: 16px;
        }
        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            .search-filters {
                justify-content: center;
            }
            .order-info {
                grid-template-columns: 1fr;
            }
            .order-actions {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1>订单管理</h1>
                <div class="breadcrumb">
                    <a href="/">首页</a> / 订单管理
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="search-filters">
                <input type="text" id="searchInput" placeholder="搜索订单号、住客姓名...">
                <select id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="pending">待确认</option>
                    <option value="confirmed">已确认</option>
                    <option value="checked_in">已入住</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
                <select id="roomFilter">
                    <option value="">全部房间</option>
                    <!-- 动态加载房间选项 -->
                </select>
                <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
            </div>
            <div>
                <button class="btn btn-primary" onclick="showNewOrderModal()">+ 新建订单</button>
                <button class="btn btn-warning" onclick="toggleMergeMode()">🔗 合并模式</button>
            </div>
        </div>

        <!-- 订单合并区域 -->
        <div class="merge-section" id="mergeSection" style="display: none;">
            <div class="merge-title">
                🔗 订单合并模式
            </div>
            <div class="merge-controls">
                <span>已选择 <strong id="selectedCount">0</strong> 个订单</span>
                <button class="btn btn-success" onclick="mergeSelectedOrders()">合并选中订单</button>
                <button class="btn btn-secondary" onclick="cancelMergeMode()">取消</button>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="orders-grid" id="ordersContainer">
            <!-- 订单卡片将通过JavaScript动态加载 -->
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
            <h3>暂无订单</h3>
            <p>还没有任何订单记录，点击"新建订单"开始创建</p>
            <button class="btn btn-primary" onclick="showNewOrderModal()">新建订单</button>
        </div>
    </div>

    <!-- 新建订单模态框 -->
    <div id="newOrderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 24px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
            <h3 style="margin: 0 0 20px 0;">新建订单</h3>
            <form id="newOrderForm">
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 6px; font-weight: 500;">选择房间 *</label>
                    <select name="room_id" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">请选择房间</option>
                        <!-- 动态加载房间选项 -->
                    </select>
                </div>
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 6px; font-weight: 500;">住客信息</label>
                    <div id="guestsContainer">
                        <div class="guest-form" style="border: 1px solid #e7e7e7; border-radius: 4px; padding: 16px; margin-bottom: 12px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                                <input type="text" name="guest_name[]" placeholder="住客姓名" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <select name="guest_gender[]" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="">选择性别</option>
                                    <option value="male">男</option>
                                    <option value="female">女</option>
                                </select>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                <input type="tel" name="guest_phone[]" placeholder="联系电话" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <input type="text" name="guest_idcard[]" placeholder="身份证号" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                    </div>
                    <button type="button" onclick="addGuestForm()" style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">+ 添加住客</button>
                </div>
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                    <button type="button" onclick="closeNewOrderModal()" style="background: #f3f3f3; color: #666; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">取消</button>
                    <button type="submit" style="background: #0052d9; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">创建订单</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let orders = [];
        let mergeMode = false;
        let selectedOrders = new Set();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
            loadRoomOptions();
            setupEventListeners();
        });

        // 加载订单列表
        function loadOrders() {
            fetch('/orders')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        orders = data.data || [];
                        renderOrders();
                    } else {
                        console.error('加载订单失败:', data.message);
                        showEmptyState();
                    }
                })
                .catch(error => {
                    console.error('网络错误:', error);
                    showEmptyState();
                });
        }

        // 渲染订单列表
        function renderOrders(filteredOrders = null) {
            const container = document.getElementById('ordersContainer');
            const emptyState = document.getElementById('emptyState');
            const ordersToRender = filteredOrders || orders;

            if (ordersToRender.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'grid';
            emptyState.style.display = 'none';

            container.innerHTML = ordersToRender.map(order => `
                <div class="order-card ${selectedOrders.has(order.id) ? 'selected' : ''}" data-order-id="${order.id}">
                    <div class="order-header">
                        <div class="order-number">${order.order_number}</div>
                        <div class="order-status status-${order.order_status}">${getStatusText(order.order_status)}</div>
                    </div>
                    <div class="order-content">
                        <div class="order-info">
                            <div class="info-item">
                                <div class="info-icon" style="background: #e6f7ff; color: #1890ff;">🏨</div>
                                <div class="info-content">
                                    <h4>房间</h4>
                                    <p>${order.room ? order.room.room_number : '未分配'}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: #f6ffed; color: #52c41a;">👥</div>
                                <div class="info-content">
                                    <h4>床位数</h4>
                                    <p>${order.bed_count} 个</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: #fff7e6; color: #fa8c16;">💰</div>
                                <div class="info-content">
                                    <h4>总金额</h4>
                                    <p>¥${order.total_amount || 0}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon" style="background: #f9f0ff; color: #722ed1;">📅</div>
                                <div class="info-content">
                                    <h4>创建时间</h4>
                                    <p>${formatDate(order.created_at)}</p>
                                </div>
                            </div>
                        </div>
                        ${order.guests && order.guests.length > 0 ? `
                        <div class="guests-section">
                            <div class="guests-title">住客信息 (${order.guests.length}人)</div>
                            <div class="guests-list">
                                ${order.guests.map(guest => `
                                    <div class="guest-tag">
                                        <div class="guest-avatar">${guest.name.charAt(0)}</div>
                                        <span>${guest.name} (${guest.gender === 'male' ? '男' : '女'})</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    <div class="order-actions">
                        <div>
                            ${mergeMode ? `
                                <label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">
                                    <input type="checkbox" ${selectedOrders.has(order.id) ? 'checked' : ''} onchange="toggleOrderSelection(${order.id})">
                                    <span>选择合并</span>
                                </label>
                            ` : ''}
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-secondary" onclick="viewOrderDetail(${order.id})">详情</button>
                            ${order.order_status === 'pending' ? `
                                <button class="btn btn-success" onclick="confirmOrder(${order.id})">确认</button>
                            ` : ''}
                            ${order.order_status === 'confirmed' ? `
                                <button class="btn btn-primary" onclick="checkInOrder(${order.id})">入住</button>
                            ` : ''}
                            ${order.order_status === 'checked_in' ? `
                                <button class="btn btn-warning" onclick="checkOutOrder(${order.id})">退房</button>
                            ` : ''}
                            <button class="btn btn-secondary" onclick="splitOrder(${order.id})">拆分</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待确认',
                'confirmed': '已确认',
                'checked_in': '已入住',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        }

        // 显示空状态
        function showEmptyState() {
            document.getElementById('ordersContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
        }

        // 切换合并模式
        function toggleMergeMode() {
            mergeMode = !mergeMode;
            selectedOrders.clear();
            document.getElementById('mergeSection').style.display = mergeMode ? 'block' : 'none';
            updateSelectedCount();
            renderOrders();
        }

        // 取消合并模式
        function cancelMergeMode() {
            mergeMode = false;
            selectedOrders.clear();
            document.getElementById('mergeSection').style.display = 'none';
            renderOrders();
        }

        // 切换订单选择
        function toggleOrderSelection(orderId) {
            if (selectedOrders.has(orderId)) {
                selectedOrders.delete(orderId);
            } else {
                selectedOrders.add(orderId);
            }
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = selectedOrders.size;
        }

        // 合并选中订单
        function mergeSelectedOrders() {
            if (selectedOrders.size < 2) {
                alert('请至少选择2个订单进行合并');
                return;
            }

            if (confirm(`确认要合并选中的 ${selectedOrders.size} 个订单吗？`)) {
                const orderIds = Array.from(selectedOrders);
                
                fetch('/orders/merge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        order_ids: orderIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('订单合并成功！');
                        cancelMergeMode();
                        loadOrders();
                    } else {
                        alert('合并失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络请求失败，请重试。');
                });
            }
        }

        // 拆分订单
        function splitOrder(orderId) {
            if (confirm('确认要拆分此订单吗？拆分后每个住客将生成独立的订单。')) {
                fetch(`/orders/${orderId}/split`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('订单拆分成功！');
                        loadOrders();
                    } else {
                        alert('拆分失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络请求失败，请重试。');
                });
            }
        }

        // 其他功能函数
        function viewOrderDetail(orderId) {
            window.open(`/orders/${orderId}`, '_blank');
        }

        function confirmOrder(orderId) {
            // 确认订单逻辑
            updateOrderStatus(orderId, 'confirmed');
        }

        function checkInOrder(orderId) {
            // 入住逻辑
            updateOrderStatus(orderId, 'checked_in');
        }

        function checkOutOrder(orderId) {
            // 退房逻辑
            updateOrderStatus(orderId, 'completed');
        }

        function updateOrderStatus(orderId, status) {
            fetch(`/orders/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadOrders();
                } else {
                    alert('操作失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络请求失败，请重试。');
            });
        }

        // 新建订单相关函数
        function showNewOrderModal() {
            document.getElementById('newOrderModal').style.display = 'block';
        }

        function closeNewOrderModal() {
            document.getElementById('newOrderModal').style.display = 'none';
            document.getElementById('newOrderForm').reset();
        }

        function addGuestForm() {
            const container = document.getElementById('guestsContainer');
            const guestForm = document.createElement('div');
            guestForm.className = 'guest-form';
            guestForm.style.cssText = 'border: 1px solid #e7e7e7; border-radius: 4px; padding: 16px; margin-bottom: 12px;';
            guestForm.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                    <input type="text" name="guest_name[]" placeholder="住客姓名" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <select name="guest_gender[]" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">选择性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 12px;">
                    <input type="tel" name="guest_phone[]" placeholder="联系电话" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <input type="text" name="guest_idcard[]" placeholder="身份证号" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <button type="button" onclick="removeGuestForm(this)" style="background: #ff4d4f; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">删除</button>
                </div>
            `;
            container.appendChild(guestForm);
        }

        function removeGuestForm(button) {
            button.closest('.guest-form').remove();
        }

        function loadRoomOptions() {
            fetch('/rooms')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const roomSelects = document.querySelectorAll('select[name="room_id"], #roomFilter');
                        roomSelects.forEach(select => {
                            if (select.id !== 'roomFilter') {
                                select.innerHTML = '<option value="">请选择房间</option>';
                            }
                            data.data.forEach(room => {
                                const option = document.createElement('option');
                                option.value = room.id;
                                option.textContent = `${room.room_number} - ${room.room_type} (${room.occupied_beds}/${room.total_beds})`;
                                select.appendChild(option);
                            });
                        });
                    }
                })
                .catch(error => {
                    console.error('加载房间选项失败:', error);
                });
        }

        function setupEventListeners() {
            // 搜索和筛选
            document.getElementById('searchInput').addEventListener('input', filterOrders);
            document.getElementById('statusFilter').addEventListener('change', filterOrders);
            document.getElementById('roomFilter').addEventListener('change', filterOrders);

            // 新建订单表单提交
            document.getElementById('newOrderForm').addEventListener('submit', function(e) {
                e.preventDefault();
                createNewOrder();
            });
        }

        function filterOrders() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const roomFilter = document.getElementById('roomFilter').value;

            const filtered = orders.filter(order => {
                const matchesSearch = !searchTerm || 
                    order.order_number.toLowerCase().includes(searchTerm) ||
                    (order.guests && order.guests.some(guest => guest.name.toLowerCase().includes(searchTerm)));
                
                const matchesStatus = !statusFilter || order.order_status === statusFilter;
                const matchesRoom = !roomFilter || (order.room && order.room.id == roomFilter);

                return matchesSearch && matchesStatus && matchesRoom;
            });

            renderOrders(filtered);
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('roomFilter').value = '';
            renderOrders();
        }

        function createNewOrder() {
            const formData = new FormData(document.getElementById('newOrderForm'));
            
            // 收集住客信息
            const guests = [];
            const names = formData.getAll('guest_name[]');
            const genders = formData.getAll('guest_gender[]');
            const phones = formData.getAll('guest_phone[]');
            const idcards = formData.getAll('guest_idcard[]');
            
            for (let i = 0; i < names.length; i++) {
                if (names[i] && genders[i] && phones[i] && idcards[i]) {
                    guests.push({
                        name: names[i],
                        gender: genders[i],
                        phone: phones[i],
                        id_card: idcards[i]
                    });
                }
            }

            // 检查性别一致性
            const genderSet = new Set(guests.map(g => g.gender));
            if (genderSet.size > 1) {
                alert('性别冲突：所有住客必须为同一性别！');
                return;
            }

            const requestData = {
                room_id: parseInt(formData.get('room_id')),
                guests: guests
            };

            fetch('/orders/new', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('订单创建成功！');
                    closeNewOrderModal();
                    loadOrders();
                } else {
                    alert('创建失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络请求失败，请重试。');
            });
        }
    </script>
</body>
</html>