<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    
    <!-- TDesign CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tdesign-web-components@1.0.0/dist/tdesign.min.css">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/static/css/main.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tdesign-icons-web@0.3.0/dist/index.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <div class="logo">
                    <i class="t-icon t-icon-home"></i>
                    <span class="logo-text">拼床住宿管理系统</span>
                </div>
            </div>
            <div class="header-center">
                <nav class="nav-menu">
                    <a href="/" class="nav-item {{if eq .PageName "房态管理"}}active{{end}}">
                        <i class="t-icon t-icon-view-module"></i>
                        房态管理
                    </a>
                    <a href="/guests" class="nav-item {{if eq .PageName "住客管理"}}active{{end}}">
                        <i class="t-icon t-icon-user"></i>
                        住客管理
                    </a>
                    <a href="/orders" class="nav-item {{if eq .PageName "订单管理"}}active{{end}}">
                        <i class="t-icon t-icon-order-descending"></i>
                        订单管理
                    </a>
                </nav>
            </div>
            <div class="header-right">
                <div class="header-info">
                    <span class="date" id="current-date"></span>
                    <span class="admin">管理员</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        {{.LayoutContent}}
    </main>

    <!-- TDesign JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/tdesign-web-components@1.0.0/dist/tdesign.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="/static/js/main.js"></script>
    
    <!-- 页面特定脚本 -->
    {{block "scripts" .}}{{end}}
</body>
</html>