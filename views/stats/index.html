<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - 拼床住宿管理系统</title>
    <link rel="stylesheet" href="/static/css/tdesign.min.css">
    <link rel="stylesheet" href="/static/css/stats.css">
</head>
<body>
    <div class="t-layout">
        <!-- 侧边栏 -->
        <aside class="t-layout__sider">
            <div class="sidebar-header">
                <h2>拼床住宿管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul class="t-menu t-menu--vertical">
                    <li class="t-menu__item">
                        <a href="/" class="t-menu__item-link">
                            <span>房态管理</span>
                        </a>
                    </li>
                    <li class="t-menu__item">
                        <a href="/orders" class="t-menu__item-link">
                            <span>订单管理</span>
                        </a>
                    </li>
                    <li class="t-menu__item t-menu__item--active">
                        <a href="/stats" class="t-menu__item-link">
                            <span>统计报表</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="t-layout__content">
            <div class="content-header">
                <h1>{{.PageName}}</h1>
                <div class="header-actions">
                    <button class="t-button t-button--primary" onclick="refreshStats()">
                        刷新数据
                    </button>
                    <button class="t-button t-button--default" onclick="exportStats()">
                        导出报表
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-overview">
                <div class="t-row t-row--gutter-16">
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon room-icon"></div>
                            <div class="stat-content">
                                <div class="stat-value" id="total-rooms">-</div>
                                <div class="stat-label">总房间数</div>
                            </div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bed-icon"></div>
                            <div class="stat-content">
                                <div class="stat-value" id="occupied-beds">-</div>
                                <div class="stat-label">已入住床位</div>
                            </div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon order-icon"></div>
                            <div class="stat-content">
                                <div class="stat-value" id="active-orders">-</div>
                                <div class="stat-label">活跃订单</div>
                            </div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon revenue-icon"></div>
                            <div class="stat-content">
                                <div class="stat-value" id="total-revenue">-</div>
                                <div class="stat-label">总收入</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-section">
                <div class="t-row t-row--gutter-16">
                    <!-- 入住率趋势图 -->
                    <div class="t-col t-col-12 t-col-lg-8">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>入住率趋势</h3>
                                <div class="chart-controls">
                                    <select class="t-select" id="trend-period">
                                        <option value="7">近7天</option>
                                        <option value="30">近30天</option>
                                        <option value="90">近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-content">
                                <canvas id="occupancy-trend-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 性别分布饼图 -->
                    <div class="t-col t-col-12 t-col-lg-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>性别分布</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="gender-distribution-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="t-row t-row--gutter-16" style="margin-top: 16px;">
                    <!-- 收入统计图 -->
                    <div class="t-col t-col-12 t-col-lg-8">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>收入统计</h3>
                                <div class="chart-controls">
                                    <select class="t-select" id="revenue-period">
                                        <option value="day">按天</option>
                                        <option value="week">按周</option>
                                        <option value="month" selected>按月</option>
                                        <option value="year">按年</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-content">
                                <canvas id="revenue-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 房间类型统计 -->
                    <div class="t-col t-col-12 t-col-lg-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>房间类型分布</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="room-type-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时数据 -->
            <div class="realtime-section">
                <div class="section-header">
                    <h3>实时数据</h3>
                    <span class="update-time">最后更新: <span id="last-update">-</span></span>
                </div>
                <div class="t-row t-row--gutter-16">
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="realtime-card">
                            <div class="realtime-value" id="current-occupancy">-</div>
                            <div class="realtime-label">当前入住人数</div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="realtime-card">
                            <div class="realtime-value" id="today-checkins">-</div>
                            <div class="realtime-label">今日入住</div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="realtime-card">
                            <div class="realtime-value" id="today-checkouts">-</div>
                            <div class="realtime-label">今日退房</div>
                        </div>
                    </div>
                    <div class="t-col t-col-6 t-col-md-3">
                        <div class="realtime-card">
                            <div class="realtime-value" id="available-beds">-</div>
                            <div class="realtime-label">可用床位</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/js/chart.min.js"></script>
    <script src="/static/js/stats.js"></script>
</body>
</html>