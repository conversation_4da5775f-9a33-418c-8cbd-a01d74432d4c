{{template "layout/base.html" .}}

{{define "LayoutContent"}}
<div class="room-management">
    <!-- 房间状态筛选栏 -->
    <div class="filter-bar">
        <div class="filter-container">
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all">
                    <span class="tab-text">全部房间</span>
                    <span class="tab-count">{{.Stats.TotalRooms}}</span>
                </button>
                <button class="filter-tab" data-filter="available">
                    <span class="tab-text">空闲房间</span>
                    <span class="tab-count">{{.Stats.AvailableRooms}}</span>
                </button>
                <button class="filter-tab" data-filter="male">
                    <span class="tab-text">男性房间</span>
                    <span class="tab-count">{{.Stats.MaleRooms}}</span>
                </button>
                <button class="filter-tab" data-filter="female">
                    <span class="tab-text">女性房间</span>
                    <span class="tab-count">{{.Stats.FemaleRooms}}</span>
                </button>
                <button class="filter-tab" data-filter="full">
                    <span class="tab-text">已满房间</span>
                    <span class="tab-count">{{.Stats.FullRooms}}</span>
                </button>
            </div>
            
            <div class="filter-actions">
                <button class="btn btn-primary" id="refreshBtn">
                    <i class="t-icon t-icon-refresh"></i>
                    刷新
                </button>
                <button class="btn btn-success" id="quickAssignBtn">
                    <i class="t-icon t-icon-add"></i>
                    一键分配
                </button>
            </div>
        </div>
    </div>

    <!-- 房间网格展示区 -->
    <div class="room-grid-container">
        <div class="room-grid" id="roomGrid">
            {{range .Rooms}}
            <div class="room-card" 
                 data-room-id="{{.Id}}" 
                 data-status="{{.RoomStatus}}" 
                 data-gender="{{.CurrentGender}}"
                 data-available="{{if .IsAvailable}}true{{else}}false{{end}}">
                
                <!-- 房间状态指示器 -->
                <div class="room-status-indicator {{.RoomStatus}} {{.CurrentGender}}"></div>
                
                <!-- 房间号 -->
                <div class="room-number">{{.RoomNumber}}</div>
                
                <!-- 床位信息 -->
                <div class="bed-info">
                    <div class="bed-count">
                        <span class="occupied">{{.OccupiedBeds}}</span>
                        <span class="separator">/</span>
                        <span class="total">{{.TotalBeds}}</span>
                    </div>
                    <div class="bed-label">床位</div>
                </div>
                
                <!-- 性别标识 -->
                <div class="gender-indicator">
                    {{if eq .CurrentGender "male"}}
                        <i class="t-icon t-icon-user gender-male"></i>
                        <span>男</span>
                    {{else if eq .CurrentGender "female"}}
                        <i class="t-icon t-icon-user gender-female"></i>
                        <span>女</span>
                    {{else}}
                        <i class="t-icon t-icon-user-clear gender-none"></i>
                        <span>空</span>
                    {{end}}
                </div>
                
                <!-- 房间状态 -->
                <div class="room-status-text">
                    {{if eq .RoomStatus "available"}}空闲
                    {{else if eq .RoomStatus "occupied"}}入住
                    {{else if eq .RoomStatus "full"}}已满
                    {{else if eq .RoomStatus "maintenance"}}维护
                    {{end}}
                </div>
                
                <!-- 操作按钮 -->
                <div class="room-actions">
                    {{if .IsAvailable}}
                        <button class="btn btn-sm btn-primary assign-btn" data-room-id="{{.Id}}">
                            <i class="t-icon t-icon-add"></i>
                            分配
                        </button>
                    {{end}}
                    <button class="btn btn-sm btn-outline detail-btn" data-room-id="{{.Id}}">
                        <i class="t-icon t-icon-view-list"></i>
                        详情
                    </button>
                </div>
                
                <!-- 楼层标识 -->
                {{if .FloorNumber}}
                <div class="floor-indicator">{{.FloorNumber}}F</div>
                {{end}}
            </div>
            {{end}}
        </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-panel">
        <div class="panel-header">
            <h3>快速操作</h3>
        </div>
        
        <div class="panel-content">
            <!-- 房态统计 -->
            <div class="stats-section">
                <h4>房态统计</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{{.Stats.TotalBeds}}</div>
                        <div class="stat-label">总床位</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{.Stats.OccupiedBeds}}</div>
                        <div class="stat-label">已占用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{.Stats.AvailableBeds}}</div>
                        <div class="stat-label">可用床位</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="occupancy-rate">0.0%</div>
                        <div class="stat-label">入住率</div>
                    </div>
                </div>
            </div>
            
            <!-- 快速分配 -->
            <div class="quick-assign-section">
                <h4>快速分配</h4>
                <form id="quickAssignForm">
                    <div class="form-group">
                        <label>住客姓名</label>
                        <input type="text" name="guest_name" class="form-control" placeholder="请输入住客姓名" required>
                    </div>
                    <div class="form-group">
                        <label>性别</label>
                        <select name="guest_gender" class="form-control" required>
                            <option value="">请选择性别</option>
                            <option value="male">男性</option>
                            <option value="female">女性</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>联系电话</label>
                        <input type="tel" name="guest_phone" class="form-control" placeholder="请输入联系电话">
                    </div>
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="t-icon t-icon-check"></i>
                        智能分配
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 性别冲突预警弹窗 -->
<div id="genderConflictModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>性别冲突警告</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="warning-icon">
                <i class="t-icon t-icon-error-circle"></i>
            </div>
            <div class="warning-message" id="conflictMessage"></div>
            <div class="warning-actions">
                <button class="btn btn-outline" id="cancelAssign">取消分配</button>
                <button class="btn btn-warning" id="forceAssign">强制分配</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/room-management.js"></script>
{{end}}