<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>住客分配 - {{.Room.RoomNumber}} - 拼床住宿管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/@tdesign/web-vue@1.6.7/dist/tdesign.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #0052d9 0%, #003ba3 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .breadcrumb {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }
        .breadcrumb a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
        }
        .breadcrumb a:hover {
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .room-info-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            margin-bottom: 20px;
        }
        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e7e7e7;
        }
        .room-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        .room-status {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-available { background: #f6ffed; color: #389e0d; }
        .status-partial { background: #fff7e6; color: #d48806; }
        .status-full { background: #fff2f0; color: #cf1322; }
        .room-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .detail-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #fafafa;
            border-radius: 6px;
        }
        .detail-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
        }
        .detail-content h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            color: #666;
        }
        .detail-content p {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .beds-visual {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .bed-item {
            width: 60px;
            height: 60px;
            border: 2px solid #e7e7e7;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            position: relative;
        }
        .bed-occupied {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #cf1322;
        }
        .bed-available {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #389e0d;
        }
        .assignment-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e7e7e7;
        }
        .current-guests {
            margin-bottom: 30px;
        }
        .guest-list {
            display: grid;
            gap: 12px;
        }
        .guest-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #0052d9;
        }
        .guest-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #0052d9;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 16px;
        }
        .guest-info h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            color: #333;
        }
        .guest-info p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .guest-actions {
            margin-left: auto;
            display: flex;
            gap: 8px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .btn-primary {
            background: #0052d9;
            color: white;
        }
        .btn-primary:hover {
            background: #003ba3;
        }
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        .btn-danger:hover {
            background: #d32029;
        }
        .btn-secondary {
            background: #f3f3f3;
            color: #666;
        }
        .btn-secondary:hover {
            background: #e6e6e6;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        .gender-warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            display: none;
        }
        .gender-warning.show {
            display: block;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        .empty-state img {
            width: 120px;
            height: 120px;
            opacity: 0.5;
            margin-bottom: 16px;
        }
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            .room-details {
                grid-template-columns: 1fr;
            }
            .header-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div>
                <h1>住客分配管理</h1>
                <div class="breadcrumb">
                    <a href="/">首页</a> / <a href="/rooms">房态管理</a> / 住客分配
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 房间信息卡片 -->
        <div class="room-info-card">
            <div class="room-header">
                <div class="room-title">{{.Room.RoomNumber}} - {{.Room.RoomType}}</div>
                <div class="room-status status-{{.Room.RoomStatus}}">
                    {{if eq .Room.RoomStatus "available"}}空闲{{end}}
                    {{if eq .Room.RoomStatus "partial"}}部分占用{{end}}
                    {{if eq .Room.RoomStatus "full"}}已满{{end}}
                </div>
            </div>

            <div class="room-details">
                <div class="detail-item">
                    <div class="detail-icon" style="background: #e6f7ff; color: #1890ff;">🛏️</div>
                    <div class="detail-content">
                        <h4>床位信息</h4>
                        <p>{{.Room.OccupiedBeds}}/{{.Room.TotalBeds}} 已占用</p>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-icon" style="background: #f6ffed; color: #52c41a;">👥</div>
                    <div class="detail-content">
                        <h4>当前性别</h4>
                        <p>{{if .Room.CurrentGender}}{{if eq .Room.CurrentGender "male"}}男性{{else}}女性{{end}}{{else}}未设定{{end}}</p>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-icon" style="background: #fff7e6; color: #fa8c16;">💰</div>
                    <div class="detail-content">
                        <h4>房间价格</h4>
                        <p>¥{{.Room.Price}}/晚</p>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-icon" style="background: #f9f0ff; color: #722ed1;">📍</div>
                    <div class="detail-content">
                        <h4>楼层位置</h4>
                        <p>{{.Room.Floor}}楼</p>
                    </div>
                </div>
            </div>

            <!-- 床位可视化 -->
            <div class="beds-visual">
                {{range $i := .BedNumbers}}
                    {{if lt $i $.Room.OccupiedBeds}}
                        <div class="bed-item bed-occupied">床位{{add $i 1}}<br>已占用</div>
                    {{else}}
                        <div class="bed-item bed-available">床位{{add $i 1}}<br>空闲</div>
                    {{end}}
                {{end}}
            </div>
        </div>

        <!-- 住客分配区域 -->
        <div class="assignment-section">
            <div class="section-title">当前住客</div>
            
            <div class="current-guests">
                {{if .CurrentGuests}}
                    <div class="guest-list">
                        {{range .CurrentGuests}}
                        <div class="guest-item">
                            <div class="guest-avatar">{{substr .Name 0 1}}</div>
                            <div class="guest-info">
                                <h4>{{.Name}}</h4>
                                <p>{{if eq .Gender "male"}}男{{else}}女{{end}} | {{.Phone}} | 入住时间: {{.CreatedAt.Format "2006-01-02"}}</p>
                            </div>
                            <div class="guest-actions">
                                <button class="btn btn-secondary" onclick="viewGuestDetail({{.Id}})">详情</button>
                                <button class="btn btn-danger" onclick="checkoutGuest({{.Id}})">退房</button>
                            </div>
                        </div>
                        {{end}}
                    </div>
                {{else}}
                    <div class="empty-state">
                        <div style="font-size: 48px; margin-bottom: 16px;">🏨</div>
                        <h3>暂无住客</h3>
                        <p>该房间目前没有住客，您可以为其分配新的住客</p>
                    </div>
                {{end}}
            </div>

            {{if lt .Room.OccupiedBeds .Room.TotalBeds}}
            <div class="section-title">添加新住客</div>
            
            <div class="gender-warning" id="genderWarning">
                ⚠️ 性别冲突警告：该房间已有{{if eq .Room.CurrentGender "male"}}男性{{else}}女性{{end}}住客，只能继续分配相同性别的住客！
            </div>

            <form id="assignmentForm" class="form-section">
                <input type="hidden" name="room_id" value="{{.Room.Id}}">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="guest_name">住客姓名 *</label>
                        <input type="text" id="guest_name" name="guest_name" required placeholder="请输入住客姓名">
                    </div>
                    <div class="form-group">
                        <label for="guest_gender">性别 *</label>
                        <select id="guest_gender" name="guest_gender" required>
                            <option value="">请选择性别</option>
                            {{if or (eq .Room.CurrentGender "") (eq .Room.CurrentGender "male")}}
                                <option value="male">男</option>
                            {{end}}
                            {{if or (eq .Room.CurrentGender "") (eq .Room.CurrentGender "female")}}
                                <option value="female">女</option>
                            {{end}}
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="guest_phone">联系电话 *</label>
                        <input type="tel" id="guest_phone" name="guest_phone" required placeholder="请输入联系电话">
                    </div>
                    <div class="form-group">
                        <label for="guest_idcard">身份证号 *</label>
                        <input type="text" id="guest_idcard" name="guest_idcard" required placeholder="请输入身份证号">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="stay_nights">住宿天数</label>
                        <input type="number" id="stay_nights" name="stay_nights" value="1" min="1" max="30">
                    </div>
                    <div class="form-group">
                        <label for="notes">备注信息</label>
                        <input type="text" id="notes" name="notes" placeholder="可选备注信息">
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button type="submit" class="btn btn-primary">确认分配住客</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">重置表单</button>
                    <a href="/rooms" class="btn btn-secondary">返回房态管理</a>
                </div>
            </form>
            {{else}}
            <div class="empty-state">
                <div style="font-size: 48px; margin-bottom: 16px; color: #ff4d4f;">🚫</div>
                <h3>房间已满</h3>
                <p>该房间床位已全部占用，无法分配新的住客</p>
                <a href="/rooms" class="btn btn-primary">返回房态管理</a>
            </div>
            {{end}}
        </div>
    </div>

    <script>
        // 性别冲突检查
        function checkGenderConflict() {
            const currentGender = '{{.Room.CurrentGender}}';
            const selectedGender = document.getElementById('guest_gender').value;
            const warning = document.getElementById('genderWarning');
            
            if (currentGender && selectedGender && currentGender !== selectedGender) {
                warning.classList.add('show');
                return false;
            } else {
                warning.classList.remove('show');
                return true;
            }
        }

        // 监听性别选择变化
        document.getElementById('guest_gender').addEventListener('change', checkGenderConflict);

        // 表单提交处理
        document.getElementById('assignmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!checkGenderConflict()) {
                alert('性别冲突！请选择正确的性别。');
                return;
            }

            const formData = new FormData(this);
            
            fetch('/rooms/{{.Room.Id}}/assign', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('住客分配成功！');
                    window.location.reload();
                } else {
                    alert('分配失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络请求失败，请重试。');
            });
        });

        // 重置表单
        function resetForm() {
            document.getElementById('assignmentForm').reset();
            document.getElementById('genderWarning').classList.remove('show');
        }

        // 查看住客详情
        function viewGuestDetail(guestId) {
            window.open('/guests/' + guestId, '_blank');
        }

        // 住客退房
        function checkoutGuest(guestId) {
            if (confirm('确认要为该住客办理退房吗？')) {
                fetch('/guests/' + guestId + '/checkout', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('退房成功！');
                        window.location.reload();
                    } else {
                        alert('退房失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络请求失败，请重试。');
                });
            }
        }

        // 页面加载时检查性别冲突
        document.addEventListener('DOMContentLoaded', function() {
            const currentGender = '{{.Room.CurrentGender}}';
            if (currentGender) {
                checkGenderConflict();
            }
        });
    </script>
</body>
</html>