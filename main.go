package main

import (
	"bed_sharing_system/middleware"
	"bed_sharing_system/models"
	_ "bed_sharing_system/routers"

	"github.com/astaxie/beego"
)

func main() {
	// 初始化数据库
	models.InitDB()

	// 注册性别校验中间件
	middleware.RegisterGenderValidationMiddleware()
	middleware.RegisterBatchGenderValidationMiddleware()

	// 添加模板函数
	beego.AddFuncMap("add", func(a, b int) int {
		return a + b
	})

	beego.AddFuncMap("substr", func(s string, start, length int) string {
		if start >= len(s) {
			return ""
		}
		end := start + length
		if end > len(s) {
			end = len(s)
		}
		return s[start:end]
	})

	beego.Run()
}
